# Profile API Documentation

## Overview
The Profile API provides endpoints for users to view and manage their account information, including personal details and password management.

## Base URL
```
/api/v1/profile
```

## Authentication
All profile endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## Endpoints

### 1. Get User Profile
Retrieves the current user's profile information.

**Endpoint:** `GET /api/v1/profile`

**Headers:**
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
- `401 Unauthorized` - Invalid or missing access token
- `500 Internal Server Error` - Server error retrieving profile

---

### 2. Update User Profile
Updates the user's profile information (currently supports name only).

**Endpoint:** `PUT /api/v1/profile`

**Headers:**
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "John Smith"
}
```

**Validation Rules:**
- `name`: Required, 2-50 characters

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 900,
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "name": "John Smith",
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

**⚠️ Important:** The response now includes a new `access_token` with updated user information. The frontend should replace the current token with this new one to maintain consistency between JWT claims and the updated user data.

**Error Responses:**
- `400 Bad Request` - Validation errors
  ```json
  {
    "success": false,
    "error": "Validation failed",
    "errors": [
      {
        "field": "name",
        "message": "Name must be between 2 and 50 characters"
      }
    ]
  }
  ```
- `401 Unauthorized` - Invalid or missing access token
- `500 Internal Server Error` - Server error updating profile

---

### 3. Change Password
Changes the user's password after validating the current password.

**Endpoint:** `POST /api/v1/profile/change-password`

**Headers:**
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "current_password": "currentPassword123",
  "new_password": "newPassword456"
}
```

**Validation Rules:**
- `current_password`: Required
- `new_password`: Required, must meet password strength requirements (minimum 8 characters)

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": null
}
```

**Error Responses:**
- `400 Bad Request` - Current password incorrect
  ```json
  {
    "success": false,
    "error": "Current password is incorrect"
  }
  ```
- `400 Bad Request` - Validation errors
  ```json
  {
    "success": false,
    "error": "Validation failed",
    "errors": [
      {
        "field": "new_password",
        "message": "Password must be at least 8 characters long"
      }
    ]
  }
  ```
- `401 Unauthorized` - Invalid or missing access token
- `500 Internal Server Error` - Server error changing password

---

## Frontend Integration Examples

### JavaScript/Fetch Examples

**Get Profile:**
```javascript
const getProfile = async () => {
  const response = await fetch('/api/v1/profile', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};
```

**Update Profile:**
```javascript
const updateProfile = async (name) => {
  const response = await fetch('/api/v1/profile', {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ name })
  });
  
  const result = await response.json();
  
  // Important: Update stored token with new one from response
  if (result.success && result.data.access_token) {
    // Update your stored access token
    localStorage.setItem('access_token', result.data.access_token);
    // Or update your auth context/store
    setAccessToken(result.data.access_token);
  }
  
  return result;
};
```

**Change Password:**
```javascript
const changePassword = async (currentPassword, newPassword) => {
  const response = await fetch('/api/v1/profile/change-password', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      current_password: currentPassword,
      new_password: newPassword
    })
  });
  return response.json();
};
```

---

## Implementation Notes

### Security Considerations
- All endpoints require valid JWT authentication
- Current password verification is required for password changes
- Password hashing is handled server-side using bcrypt
- Email addresses cannot be changed (considered immutable after registration)
- **Profile updates return new JWT tokens** with updated user information

### Token Management
- **Critical:** Profile updates invalidate the current JWT claims by changing user data
- New access tokens are provided in profile update responses to maintain data consistency
- Frontend applications **must update their stored tokens** when profile updates succeed
- Old tokens remain valid until their natural expiration time

### Data Validation
- Name validation: 2-50 characters, required
- Password validation: Follows application password policy
- Input sanitization is applied to prevent injection attacks

### Error Handling
- Consistent error response format across all endpoints
- Field-level validation errors for detailed feedback
- Appropriate HTTP status codes for different error types

### Rate Limiting
Consider implementing rate limiting for password change endpoints to prevent brute force attacks.

### Audit Logging
Consider logging profile changes and password changes for security auditing purposes.