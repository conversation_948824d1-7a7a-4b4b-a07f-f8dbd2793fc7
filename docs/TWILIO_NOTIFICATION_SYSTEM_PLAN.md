# Twilio Notification System Implementation Plan

## Overview
This document outlines the comprehensive plan to implement a Twilio-based reminder system for rent payments, overdue notifications, and maintenance issue resolved messages in the Rentbase application.

## Current Codebase Analysis Summary

### Backend Architecture
- **Framework**: Go with Chi router
- **Database**: PostgreSQL
- **Authentication**: JWT
- **Key Models**: Payments, Leases, Maintenance, Tenants (with phone numbers)

### Missing Components
- No existing Twilio integration
- No notification system
- No background job scheduler

---

## 1. Notification Service Architecture

### Core Components Structure
```
backend/
├── internal/
│   ├── notification/
│   │   ├── service.go          # Main notification service
│   │   ├── twilio_client.go    # Twilio API wrapper
│   │   ├── templates.go        # SMS message templates
│   │   └── scheduler.go        # Background job scheduler
│   ├── storage/
│   │   └── notification_store.go # Database operations
│   └── api/handlers/
│       └── notification.go     # API endpoints
```

### Database Schema

```sql
-- Notification templates
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL, -- 'rent_reminder', 'rent_overdue', 'maintenance_resolved'
    message_template TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification queue
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    lease_id UUID REFERENCES leases(id),
    maintenance_id UUID REFERENCES maintenance_requests(id),
    type VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'cancelled'
    scheduled_at TIMESTAMPTZ NOT NULL,
    sent_at TIMESTAMPTZ,
    error_message TEXT,
    twilio_sid VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification settings per tenant
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    rent_reminders_enabled BOOLEAN DEFAULT true,
    reminder_days_before INTEGER DEFAULT 3,
    overdue_notifications_enabled BOOLEAN DEFAULT true,
    maintenance_notifications_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 2. Rent Payment Reminder Implementation

### Trigger Logic
- **Schedule**: Daily cron job at 9:00 AM
- **Target**: Active leases where next rent is due in X days (configurable, default 3)
- **Conditions**:
  - Lease is active
  - Tenant has phone number
  - No payment received for current period
  - Tenant has reminders enabled

### Message Template
```
"Hi {tenant_name}, this is a friendly reminder that your rent payment of {amount} for {unit_name} is due on {due_date}. Please make your payment to avoid late fees. Thank you!"
```

### Implementation Steps
1. Create `CalculateNextRentDue()` function using lease start date and rent cycle
2. Check payment history to determine if rent is already paid
3. Generate notifications for qualifying leases
4. Queue notifications for sending

---

## 3. Rent Overdue Notification Implementation

### Trigger Logic
- **Schedule**: Daily cron job at 10:00 AM
- **Target**: Active leases where rent is overdue
- **Conditions**:
  - Lease is active
  - Rent due date has passed
  - No payment received for overdue period
  - Tenant has phone number

### Message Template
```
"Dear {tenant_name}, your rent payment of {amount} for {unit_name} was due on {due_date} and is now overdue. Please contact us immediately to arrange payment. Late fees may apply."
```

### Implementation Steps
1. Identify overdue leases by comparing due dates with payment history
2. Calculate overdue amount and days
3. Generate escalating notifications (1 day, 3 days, 7 days overdue)
4. Queue notifications with appropriate urgency

---

## 4. Maintenance Issue Resolved Notification

### Trigger Logic
- **Event-based**: Triggered when maintenance status changes to "resolved"
- **Target**: Tenant associated with the maintenance request
- **Conditions**:
  - Maintenance request has associated lease/tenant
  - Tenant has phone number
  - Tenant has maintenance notifications enabled

### Message Template
```
"Hi {tenant_name}, we're pleased to inform you that your maintenance request '{title}' for {unit_name} has been resolved. Thank you for your patience!"
```

### Implementation Steps
1. Add webhook/event trigger in maintenance update handler
2. Generate notification when status changes to "resolved"
3. Include maintenance details and resolution info
4. Send notification immediately

---

## 5. Background Job Scheduling System

### Scheduler Implementation
```go
type NotificationScheduler struct {
    service *NotificationService
    ticker  *time.Ticker
}

// Jobs to implement:
// 1. DailyRentReminderJob() - runs at 9:00 AM
// 2. DailyOverdueCheckJob() - runs at 10:00 AM
// 3. ProcessPendingNotificationsJob() - runs every 5 minutes
// 4. RetryFailedNotificationsJob() - runs every hour
```

### Cron Schedule
- Rent reminders: `0 9 * * *` (9:00 AM daily)
- Overdue checks: `0 10 * * *` (10:00 AM daily)
- Process queue: `*/5 * * * *` (every 5 minutes)
- Retry failed: `0 * * * *` (every hour)

---

## 6. Twilio Integration

### Required Dependencies
```go
// Add to go.mod
github.com/twilio/twilio-go v1.15.0
github.com/robfig/cron/v3 v3.0.1
```

### Configuration
```go
type TwilioConfig struct {
    AccountSID string
    AuthToken  string
    FromNumber string
}
```

### Environment Variables
```
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********
```

---

## 7. API Endpoints

### New Endpoints
```
POST   /api/notifications/test-send          # Test notification sending
GET    /api/notifications/history/:tenant_id # Notification history
PUT    /api/notifications/preferences        # Update preferences
GET    /api/notifications/templates          # Get message templates
PUT    /api/notifications/templates/:id      # Update templates
```

---

## 8. Error Handling & Monitoring

### Error Scenarios
- Invalid phone numbers
- Twilio API failures
- Network timeouts
- Rate limiting

### Monitoring
- Failed notification tracking
- Delivery status webhooks
- Performance metrics
- Cost monitoring

---

## 9. Testing Strategy

### Test Cases
1. **Unit Tests**: Template rendering, date calculations, validation
2. **Integration Tests**: Twilio API integration, database operations
3. **End-to-End Tests**: Complete notification workflows
4. **Mock Tests**: Test without actual SMS sending

---

## 10. Security & Compliance

### Security Measures
- Encrypt Twilio credentials
- Validate phone numbers
- Rate limiting for API endpoints
- Audit trail for all notifications

### Privacy Considerations
- Opt-out mechanism for tenants
- Data retention policies
- GDPR compliance for phone data

---

## Implementation Priority & Timeline

### Phase 1 (Week 1-2)
1. Database schema setup
2. Basic Twilio integration
3. Notification service foundation
4. Message templates

### Phase 2 (Week 3)
1. Rent reminder system
2. Background job scheduler
3. Basic API endpoints

### Phase 3 (Week 4)
1. Overdue notifications
2. Maintenance resolved notifications
3. Notification preferences UI

### Phase 4 (Week 5)
1. Error handling & retry logic
2. Monitoring & analytics
3. Testing & optimization

---

## Technical Implementation Notes

### Key Files to Create/Modify

#### Backend Files
- `backend/internal/notification/service.go`
- `backend/internal/notification/twilio_client.go`
- `backend/internal/notification/templates.go`
- `backend/internal/notification/scheduler.go`
- `backend/internal/storage/notification_store.go`
- `backend/internal/api/handlers/notification.go`

#### Database Migration
- Create migration file for notification tables

#### Configuration Updates
- Update `backend/internal/config/config.go` for Twilio settings
- Update environment files with Twilio credentials

### Frontend Integration
- Create notification preferences UI
- Add notification history display
- Integrate with existing tenant management

---

## Success Metrics

### Technical Metrics
- Notification delivery rate > 95%
- Average delivery time < 30 seconds
- System uptime > 99.5%
- Failed notification retry success rate > 80%

### Business Metrics
- Reduction in late rent payments
- Improved tenant satisfaction scores
- Decreased manual communication overhead
- Enhanced maintenance resolution tracking

---

*This plan provides a comprehensive foundation for implementing a robust Twilio-based notification system that will automate rent reminders, overdue notifications, and maintenance resolution messages while maintaining security, scalability, and user control.*