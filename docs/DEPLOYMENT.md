# RentBase Deployment Guide - Vercel + Render

## 🚀 Quick Deploy Steps

### 1. Render Backend (Database + API)
1. **Create Account**: https://render.com
2. **PostgreSQL Database**:
   - New → PostgreSQL
   - Name: `rentbase-db`
   - Plan: Free
   - Save connection string
3. **Web Service**:
   - New → Web Service
   - Connect GitHub repo
   - Root Directory: `backend`
   - Build Command: `go build -o main cmd/server/main.go`
   - Start Command: `./main`
   - Port: `10000`

### 2. Environment Variables (Render)
```
DATABASE_URL=<from-postgres-service>
JWT_SECRET=<generate-256-bit-secret>
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d
PORT=10000
ENVIRONMENT=production
CORS_ORIGINS=https://your-app.vercel.app
```

### 3. Vercel Frontend
1. **Create Account**: https://vercel.com
2. **Import Repository**:
   - Connect GitHub
   - Select repo
   - Root Directory: `frontend`
   - Framework: Next.js
3. **Environment Variables**:
```
NEXT_PUBLIC_API_BASE_URL=https://your-backend.onrender.com/api/v1
```

### 4. Testing Checklist
- [ ] Backend health check: `https://your-backend.onrender.com/api/v1/health`
- [ ] Frontend loads: `https://your-app.vercel.app`
- [ ] Registration works
- [ ] Login works
- [ ] Dashboard loads
- [ ] API calls work

## 🔧 Troubleshooting

### Backend Issues
- Check Render logs for errors
- Verify DATABASE_URL connection
- Test health endpoint

### Frontend Issues
- Check Vercel deployment logs
- Verify API_BASE_URL is correct
- Test in browser network tab

### Database Issues
- Check PostgreSQL connection
- Run migrations manually if needed
- Verify user permissions

## 🔒 Security Checklist
- [ ] Strong JWT secret (256+ bits)
- [ ] HTTPS only in production
- [ ] CORS properly configured
- [ ] Environment variables secured
- [ ] Database credentials secured

## 💰 Cost Estimate (Free Tier)
- Render: Free (750 hours/month)
- Vercel: Free (100GB bandwidth)
- PostgreSQL: Free (1GB storage)

## 🚀 Going Live
1. Update CORS_ORIGINS with your domain
2. Set up custom domain (optional)
3. Monitor logs and performance
4. Set up health monitoring

Ready to deploy! 🎉