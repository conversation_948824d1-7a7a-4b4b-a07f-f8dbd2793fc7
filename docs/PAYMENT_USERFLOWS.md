# Payment System User Flows

## Overview
The payment system enables property owners to record and track rent payments from their tenants. It integrates with the existing lease management system.

## Core User Flows

### 1. Record Payment Flow
**Actor:** Property Owner
**Trigger:** Tenant makes a rent payment

1. Owner logs into the system
2. Owner navigates to payments section OR specific lease
3. Owner clicks "Record Payment"
4. System displays payment form with:
   - Lease selection (pre-filled if coming from specific lease)
   - Amount (with rent amount as suggestion)
   - Payment date (defaults to today)
   - Payment method (dropdown: manual, mpesa, card, bank_transfer, cash, check)
   - Notes (optional)
5. Owner fills in payment details
6. Owner submits payment
7. System validates payment data
8. System saves payment to database
9. System shows success confirmation
10. System updates lease payment history
11. System updates dashboard metrics

### 2. View Payment History Flow
**Actor:** Property Owner
**Trigger:** Owner wants to see payment history

#### Option A: All Payments
1. Owner navigates to payments section
2. System displays paginated list of all payments across properties:
   - Payment date
   - Tenant name
   - Unit name
   - Amount paid
   - Payment method
   - Status indicators
3. Owner can filter by:
   - Property
   - Date range
   - Payment method
   - Tenant

#### Option B: Lease-Specific Payments
1. Owner navigates to specific lease details
2. System shows payment history for that lease
3. Owner can see payment patterns and missed payments

### 3. View Payment Analytics Flow
**Actor:** Property Owner
**Trigger:** Owner wants to understand payment trends

1. Owner visits dashboard
2. System displays payment metrics:
   - Total income this month
   - Payment collection rate
   - Recent payments
   - Overdue payments
   - Monthly trends

## API Endpoints

### Core Payment Operations
- `POST /api/v1/payments` - Record a new payment
- `GET /api/v1/payments` - Get all payments for owner
- `GET /api/v1/payments/{id}` - Get specific payment details
- `PUT /api/v1/payments/{id}` - Update payment details
- `DELETE /api/v1/payments/{id}` - Delete payment (admin only)

### Payment Queries
- `GET /api/v1/leases/{id}/payments` - Get payments for specific lease
- `GET /api/v1/payments/recent` - Get recent payments (dashboard)
- `GET /api/v1/payments/analytics` - Get payment analytics

### Payment Reporting
- `GET /api/v1/payments/export` - Export payments to CSV/Excel
- `GET /api/v1/payments/summary` - Get payment summary by period

## Data Flow

### Payment Creation
```
Frontend Form → Validation → API Handler → Service Layer → Storage Layer → Database
                    ↓
            Success Response ← JSON Response ← Payment Object ← Created Payment
```

### Payment Retrieval
```
Frontend Request → API Handler → Service Layer → Storage Layer → Database Query
                    ↓
            Payment List ← JSON Response ← Processed Data ← Raw Payment Data
```

## Security Considerations

1. **Authorization**: Only property owners can record payments for their properties
2. **Validation**: All payment amounts must be positive
3. **Audit Trail**: Track who recorded each payment and when
4. **Data Integrity**: Payments are tied to valid leases only

## Error Scenarios

1. **Invalid lease**: Payment references non-existent or inactive lease
2. **Invalid amount**: Negative or zero payment amount
3. **Unauthorized access**: User tries to record payment for property they don't own
4. **Duplicate payment**: System should allow but warn about potential duplicates
5. **Invalid payment method**: Payment method not in allowed list

## Integration Points

1. **Dashboard Service**: Payment data feeds into dashboard metrics
2. **Lease Service**: Payments are associated with leases
3. **Property Service**: Payment totals roll up to property level
4. **Notification Service**: (Future) Notify about missed payments