# Maintenance Request System User Flows

## Overview
The maintenance request system enables tenants to report maintenance issues and property owners to track, assign, and resolve these requests. It supports both unit-specific and lease-specific maintenance requests with attachments, status tracking, and cost management.

## Core User Flows

### 1. Create Maintenance Request Flow
**Actor:** Property Owner (on behalf of tenant) or Tenant
**Trigger:** Maintenance issue identified

1. User logs into the system
2. User navigates to maintenance section
3. User clicks "Create Maintenance Request"
4. System displays maintenance form with:
   - Title (required)
   - Description (detailed problem description)
   - Unit selection (if unit-specific issue)
   - Lease selection (if lease-specific issue)
   - Priority (low, medium, high, urgent)
   - Upload attachments (photos, documents)
5. User fills in request details
6. User submits maintenance request
7. System validates request data
8. System saves request with "pending" status
9. System shows success confirmation
10. System sends notification (future feature)

### 2. View Maintenance Requests Flow
**Actor:** Property Owner
**Trigger:** Owner wants to see maintenance requests

#### Option A: All Maintenance Requests
1. Owner navigates to maintenance section
2. System displays paginated list of all maintenance requests:
   - Request title and priority
   - Unit/lease information
   - Status and creation date
   - Assigned technician
   - Cost estimate
3. Owner can filter by:
   - Status (pending, in_progress, resolved, cancelled)
   - Priority (low, medium, high, urgent)
   - Property/Unit
   - Date range

#### Option B: Unit-Specific Maintenance
1. Owner navigates to specific unit details
2. System shows maintenance history for that unit
3. Owner can see patterns and recurring issues

### 3. Update Maintenance Request Flow
**Actor:** Property Owner or Assigned Technician
**Trigger:** Status change or progress update

1. User opens specific maintenance request
2. User clicks "Update Request"
3. System displays update form with:
   - Status dropdown (pending → in_progress → resolved)
   - Assigned technician field
   - Cost field (for completed work)
   - Resolution notes
   - Additional attachments
4. User updates relevant fields
5. System validates updates
6. System saves changes with timestamp
7. If status changed to "resolved", system records resolved_at timestamp
8. System shows success confirmation

### 4. Maintenance Dashboard Flow
**Actor:** Property Owner
**Trigger:** Owner wants maintenance overview

1. Owner visits maintenance dashboard
2. System displays key metrics:
   - Active requests count
   - Requests by priority
   - Average resolution time
   - Monthly maintenance costs
   - Pending vs resolved requests
   - Most problematic units

### 5. Maintenance History Flow
**Actor:** Property Owner
**Trigger:** Review historical maintenance data

1. Owner navigates to maintenance history
2. System displays chronological list of all requests
3. Owner can export data for reporting
4. System shows cost summaries and trends

## API Endpoints

### Core Maintenance Operations
- `POST /api/v1/maintenance` - Create new maintenance request
- `GET /api/v1/maintenance` - Get all maintenance requests for owner
- `GET /api/v1/maintenance/{id}` - Get specific maintenance request
- `PUT /api/v1/maintenance/{id}` - Update maintenance request
- `DELETE /api/v1/maintenance/{id}` - Delete maintenance request (soft delete)

### Maintenance Queries
- `GET /api/v1/maintenance/pending` - Get pending maintenance requests
- `GET /api/v1/maintenance/active` - Get in-progress maintenance requests
- `GET /api/v1/maintenance/unit/{id}` - Get maintenance requests for specific unit
- `GET /api/v1/maintenance/lease/{id}` - Get maintenance requests for specific lease

### Maintenance Analytics
- `GET /api/v1/maintenance/analytics` - Get maintenance analytics and metrics
- `GET /api/v1/maintenance/summary` - Get maintenance summary by status/priority
- `GET /api/v1/maintenance/costs` - Get maintenance costs summary

### Maintenance Attachments
- `POST /api/v1/maintenance/{id}/attachments` - Upload attachment to maintenance request
- `GET /api/v1/maintenance/{id}/attachments` - Get attachments for maintenance request
- `DELETE /api/v1/attachments/{id}` - Delete specific attachment

## Data Flow

### Maintenance Request Creation
```
Frontend Form → Validation → API Handler → Service Layer → Storage Layer → Database
                    ↓
            Success Response ← JSON Response ← Maintenance Object ← Created Request
```

### Status Update Flow
```
Status Update → Service Validation → Storage Update → Timestamp Recording → Notifications
                        ↓
                Status History ← Audit Trail ← Database Update
```

## Status Workflow

```
pending → in_progress → resolved
   ↓           ↓          ↑
cancelled ←←←←←←←←←←←←←←←←←┘
```

### Status Definitions
- **pending**: New request, awaiting assignment
- **in_progress**: Assigned and being worked on
- **resolved**: Issue fixed and verified
- **cancelled**: Request cancelled or deemed invalid

## Priority Levels

- **urgent**: Safety issues, major system failures (immediate attention)
- **high**: Issues affecting habitability (within 24 hours)
- **medium**: Standard repairs (within 1 week)
- **low**: Cosmetic or minor issues (within 1 month)

## Security Considerations

1. **Authorization**: Only property owners can manage maintenance requests for their properties
2. **Data Privacy**: Sensitive information restricted to authorized users
3. **Attachment Security**: File upload validation and virus scanning
4. **Audit Trail**: Track all status changes and updates
5. **Access Control**: Tenants can view their own requests, owners can view all

## Business Rules

1. **Reference Requirement**: Maintenance request must reference either unit_id or lease_id
2. **Status Progression**: Status can only move forward in workflow (except to cancelled)
3. **Cost Tracking**: Costs can only be added when status is in_progress or resolved
4. **Resolution Timestamp**: Automatically set when status changes to resolved
5. **Priority Validation**: Only valid priority levels accepted
6. **Assignment**: Only owners can assign technicians to requests

## Integration Points

1. **Unit Management**: Maintenance requests linked to specific units
2. **Lease Management**: Requests can be associated with active leases
3. **User Management**: Created_by and assigned_to fields linked to users
4. **Attachment System**: Support for photos and documents
5. **Notification Service**: (Future) Alerts for status changes and overdue requests
6. **Dashboard Service**: Maintenance metrics feed into property dashboard

## Error Scenarios

1. **Invalid Unit/Lease**: Request references non-existent or unauthorized unit/lease
2. **Invalid Status Transition**: Attempting invalid status change
3. **Missing Required Fields**: Title or reference fields missing
4. **Invalid Priority**: Priority not in allowed values
5. **Unauthorized Access**: User tries to access maintenance for property they don't own
6. **File Upload Issues**: Attachment upload fails or file type not allowed
7. **Cost Validation**: Negative cost values or invalid format

## Notification Triggers (Future Enhancement)

1. **New Request**: Notify owner when new maintenance request created
2. **Status Change**: Notify relevant parties when status changes
3. **Overdue Request**: Alert when request exceeds expected resolution time
4. **High Priority**: Immediate notification for urgent requests
5. **Cost Update**: Notify when maintenance costs are added
6. **Resolution**: Confirm completion with requester

## Reporting Capabilities

1. **Maintenance Summary**: Requests by status, priority, and time period
2. **Cost Analysis**: Total and average maintenance costs
3. **Unit Performance**: Most problematic units and common issues
4. **Response Time**: Average time to resolution by priority
5. **Technician Performance**: Requests handled and resolution rates
6. **Trend Analysis**: Seasonal patterns and recurring issues