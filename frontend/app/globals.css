@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --input-background: #ffffff;
  --input-text: #111827;
  --input-border: #d1d5db;
  --input-placeholder: #6b7280;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-input-background: var(--input-background);
  --color-input-text: var(--input-text);
  --color-input-border: var(--input-border);
  --color-input-placeholder: var(--input-placeholder);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --input-background: #1f2937;
    --input-text: #f9fafb;
    --input-border: #374151;
    --input-placeholder: #9ca3af;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Input field styling that adapts to color scheme */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
textarea,
select {
  background-color: var(--input-background);
  color: var(--input-text);
  border-color: var(--input-border);
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="number"]::placeholder,
input[type="tel"]::placeholder,
input[type="url"]::placeholder,
textarea::placeholder {
  color: var(--input-placeholder);
}
