'use client';

import { useState, useEffect } from 'react';
import { paymentsApi } from '../../lib/api';
import { Payment, PaymentSummary } from '../../types/payment';
import PaymentTable from '../../components/PaymentTable';
import PaymentModal from '../../components/PaymentModal';
import PaymentDetailModal from '../../components/PaymentDetailModal';
import { 
  Plus, 
  Filter, 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  CreditCard,
  Download
} from 'lucide-react';

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);
  
  // Filter states
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [leaseFilter, setLeaseFilter] = useState('');

  useEffect(() => {
    fetchData();
  }, [startDate, endDate, leaseFilter]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const filterParams: Record<string, string> = {};
      if (startDate) filterParams.start_date = startDate;
      if (endDate) filterParams.end_date = endDate;
      if (leaseFilter) filterParams.lease_id = leaseFilter;

      const [paymentsResponse, summaryResponse] = await Promise.all([
        paymentsApi.getPayments(filterParams),
        paymentsApi.getSummary(startDate || endDate ? { start_date: startDate, end_date: endDate } : undefined)
      ]);

      if (paymentsResponse.success) {
        setPayments((paymentsResponse.data as Payment[]) || []);
      } else {
        setError('Failed to fetch payments');
      }

      if (summaryResponse.success && summaryResponse.data) {
        setSummary(summaryResponse.data as PaymentSummary);
      }
    } catch (err) {
      console.error('Error fetching payment data:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSelected = (payment: Payment) => {
    setSelectedPayment(payment);
    setShowDetailModal(true);
  };

  const handleEdit = (payment: Payment) => {
    setEditingPayment(payment);
    setShowCreateModal(true);
    setShowDetailModal(false);
  };

  const handleModalClose = () => {
    setShowCreateModal(false);
    setShowDetailModal(false);
    setSelectedPayment(null);
    setEditingPayment(null);
  };

  const handleSuccess = () => {
    fetchData();
    handleModalClose();
  };

  const handleClearFilters = () => {
    setStartDate('');
    setEndDate('');
    setLeaseFilter('');
  };

  const handleExport = () => {
    if (payments.length === 0) {
      alert('No payments to export');
      return;
    }

    // Create CSV content
    const headers = [
      'Payment ID',
      'Tenant Name',
      'Unit Name',
      'Amount',
      'Payment Method',
      'Payment Date',
      'Created Date',
      'Notes'
    ];

    const csvContent = [
      headers.join(','),
      ...payments.map(payment => [
        `"${payment.id}"`,
        `"${payment.tenant_name || 'Unknown'}"`,
        `"${payment.unit_name || 'Unknown'}"`,
        payment.amount,
        `"${payment.payment_method || 'Not specified'}"`,
        `"${formatDate(payment.paid_at)}"`,
        `"${formatDate(payment.created_at)}"`,
        `"${payment.notes || ''}"`
      ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);

    // Generate filename with current date and filters
    const today = new Date().toISOString().split('T')[0];
    let filename = `payments-${today}`;
    if (startDate || endDate) {
      filename += `-filtered`;
    }
    filename += '.csv';

    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const summaryCards = summary ? [
    {
      name: 'Total Revenue',
      value: formatCurrency(summary.total_amount),
      icon: DollarSign,
      color: 'bg-green-500',
    },
    {
      name: 'Payment Count',
      value: summary.payment_count,
      icon: TrendingUp,
      color: 'bg-blue-500',
    },
    {
      name: 'Average Payment',
      value: formatCurrency(summary.payment_count > 0 ? summary.total_amount / summary.payment_count : 0),
      icon: CreditCard,
      color: 'bg-purple-500',
    },
  ] : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <DollarSign className="h-8 w-8 text-indigo-600" />
          <h1 className="text-2xl font-bold text-gray-900">Payments</h1>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleExport}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Record Payment
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
          {summaryCards.map((card) => {
            const Icon = card.icon;
            return (
              <div key={card.name} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className={`${card.color} p-3 rounded-md`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          {card.name}
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {card.value}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Period Info */}
      {summary && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-blue-500 mr-2" />
            <div className="text-sm text-blue-700">
              <strong>Period:</strong> {formatDate(summary.period_start)} to {formatDate(summary.period_end)}
              {summary.payment_count > 0 && (
                <span className="ml-4">
                  <strong>Average per payment:</strong> {formatCurrency(summary.total_amount / summary.payment_count)}
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <Filter className="h-5 w-5 text-gray-400 mr-2" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <div className="flex space-x-4">
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">From Date</label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">To Date</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>

            {(startDate || endDate || leaseFilter) && (
              <div className="flex items-end">
                <button
                  onClick={handleClearFilters}
                  className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payments Table */}
      <div className="bg-white shadow rounded-lg">
        {payments.length === 0 && (startDate || endDate || leaseFilter) ? (
          <div className="text-center py-12">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payments</h3>
            <p className="mt-1 text-sm text-gray-500">
              No payments match your current filters.
            </p>
            <div className="mt-6">
              <button
                onClick={handleClearFilters}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        ) : (
          <PaymentTable
            payments={payments}
            onPaymentUpdated={fetchData}
            onPaymentSelected={handlePaymentSelected}
            onPaymentEdit={handleEdit}
            showActions={true}
            onAddNew={() => setShowCreateModal(true)}
          />
        )}
      </div>

      {/* Modals */}
      <PaymentModal
        isOpen={showCreateModal}
        onClose={handleModalClose}
        onSuccess={handleSuccess}
        payment={editingPayment || undefined}
      />

      <PaymentDetailModal
        isOpen={showDetailModal}
        onClose={handleModalClose}
        onEdit={handleEdit}
        payment={selectedPayment}
      />
    </div>
  );
}