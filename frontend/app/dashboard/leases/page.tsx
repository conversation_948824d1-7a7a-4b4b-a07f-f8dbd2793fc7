
'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, FileText } from 'lucide-react';
import LeaseModal from '../../components/LeaseModal';
import LeasesTable from '../../components/LeasesTable';
import { leasesApi } from '../../lib/api';

export interface Lease {
  id: string;
  unit_id: string;
  unit_name?: string;
  property_id?: string;
  tenant_id?: string;
  tenant_name?: string;
  tenant_email?: string;
  start_date: string;
  end_date?: string;
  rent_amount: number;
  deposit_amount: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export default function LeasesPage() {
  const [leases, setLeases] = useState<Lease[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedLease, setSelectedLease] = useState<Lease | null>(null);

  const fetchLeases = async () => {
    try {
      setLoading(true);
      const response = await leasesApi.getLeases();
      if (response.success) {
        setLeases((response.data as Lease[]) || []);
      } else {
        setError(response.message || 'Failed to fetch leases');
      }
    } catch (err) {
      setError('An error occurred while fetching leases');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeases();
  }, []);

  const handleAddLease = () => {
    setSelectedLease(null);
    setIsModalOpen(true);
  };

  const handleEditLease = (lease: Lease) => {
    setSelectedLease(lease);
    setIsModalOpen(true);
  };

  const handleDeleteLease = async (leaseId: string) => {
    if (window.confirm('Are you sure you want to delete this lease?')) {
      // There is no deleteLease in the api, so we will deactivate it
      const response = await leasesApi.deactivateLease(leaseId);
      if (response.success) {
        fetchLeases(); // Refresh the list
      } else {
        alert(response.message || 'Failed to deactivate lease');
      }
    }
  };

  const handleSaveLease = async (lease: Lease) => {
    const response = selectedLease
      ? await leasesApi.updateLease(lease.id, lease) // updateLease doesn't exist, this will need to be fixed
      : await leasesApi.createLease(lease);

    if (response.success) {
      setIsModalOpen(false);
      fetchLeases(); // Refresh the list
    } else {
      alert(response.message || 'Failed to save lease');
    }
  };

  const filteredLeases = leases.filter(lease =>
    lease.tenant_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    lease.unit_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="h-8 w-8 text-indigo-600" />
          <h1 className="text-2xl font-bold text-gray-900">Leases</h1>
        </div>
        <button
          onClick={handleAddLease}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Lease
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center justify-between gap-4">
        <div className="relative w-full max-w-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by tenant or unit..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
        {/* Add filter dropdowns here if needed */}
      </div>

      {/* Loading and Error states */}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      )}
      {error && <p className="text-red-500">{error}</p>}

      {/* Leases Table */}
      {!loading && !error && (
        <>
          {filteredLeases.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No leases</h3>
              <p className="mt-1 text-sm text-gray-500">
                {leases.length === 0
                  ? "Get started by adding your first lease."
                  : "No leases match your search."
                }
              </p>
              <div className="mt-6">
                <button
                  type="button"
                  onClick={handleAddLease}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <Plus className="-ml-1 mr-2 h-5 w-5" />
                  Add Lease
                </button>
              </div>
            </div>
          ) : (
            <LeasesTable
              leases={filteredLeases}
              onEdit={handleEditLease}
              onDelete={handleDeleteLease}
              onAddNew={handleAddLease}
            />
          )}
        </>
      )}

      {/* Lease Modal */}
      <LeaseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveLease}
        lease={selectedLease}
      />
    </div>
  );
}
