'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { propertiesApi, maintenanceApi } from '../../../lib/api';
import { ArrowLeft, MapPin, Calendar, Edit, Plus, Building, Users, Wrench } from 'lucide-react';
import UnitModal from '../../../components/UnitModal';
import MaintenanceTable from '../../../components/MaintenanceTable';
import { MaintenanceRequest } from '../../../types/maintenance';

interface Property {
  id: string;
  owner_id: string;
  title: string;
  address: string;
  city?: string;
  country?: string;
  description?: string;
  units_count: number;
  created_at: string;
  updated_at: string;
}

interface Unit {
  id: string;
  property_id: string;
  name: string;
  rent_amount: number;
  status: 'vacant' | 'occupied' | 'maintenance';
  created_at: string;
  updated_at: string;
}

export default function PropertyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [property, setProperty] = useState<Property | null>(null);
  const [units, setUnits] = useState<Unit[]>([]);
  const [maintenanceRequests, setMaintenanceRequests] = useState<MaintenanceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [isUnitModalOpen, setIsUnitModalOpen] = useState(false);

  useEffect(() => {
    const fetchPropertyDetails = async () => {
      if (!params.id || typeof params.id !== 'string') return;

      try {
        const [propertyResponse, unitsResponse] = await Promise.all([
          propertiesApi.getProperty(params.id),
          propertiesApi.getPropertyUnits(params.id)
        ]);

        if (propertyResponse.success && propertyResponse.data) {
          const propertyData = propertyResponse.data as Property;
          setProperty(propertyData);
        } else {
          console.error('Failed to fetch property:', propertyResponse.message);
        }

        if (unitsResponse.success && unitsResponse.data) {
          const unitsData = unitsResponse.data as Unit[];
          setUnits(unitsData);
          
          // Fetch maintenance requests for all units in this property
          if (unitsData.length > 0) {
            const maintenancePromises = unitsData.map(unit => 
              maintenanceApi.getRequestsByUnit(unit.id)
            );
            const maintenanceResponses = await Promise.all(maintenancePromises);
            const allMaintenanceRequests: MaintenanceRequest[] = [];
            
            maintenanceResponses.forEach(response => {
              if (response.success && response.data) {
                allMaintenanceRequests.push(...(response.data as MaintenanceRequest[]));
              }
            });
            
            setMaintenanceRequests(allMaintenanceRequests);
          }
        }
      } catch (error) {
        console.error('Failed to fetch property details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPropertyDetails();
  }, [params.id]);

  const handleUnitCreated = () => {
    // Refresh units list after creating a new unit
    const fetchUnits = async () => {
      if (!params.id || typeof params.id !== 'string') return;
      
      try {
        const response = await propertiesApi.getPropertyUnits(params.id);
        if (response.success && response.data) {
          setUnits(response.data as Unit[]);
        }
      } catch (error) {
        console.error('Failed to refresh units:', error);
      }
    };

    fetchUnits();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="text-center py-12">
        <Building className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Property not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The property you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
        </p>
        <div className="mt-6">
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <ArrowLeft className="-ml-1 mr-2 h-5 w-5" />
            Back to Properties
          </button>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'occupied':
        return 'bg-green-100 text-green-800';
      case 'vacant':
        return 'bg-yellow-100 text-yellow-800';
      case 'maintenance':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{property.title}</h1>
            <div className="flex items-center text-gray-500 mt-1">
              <MapPin className="h-4 w-4 mr-1" />
              <span>
                {property.address}
                {property.city && `, ${property.city}`}
                {property.country && `, ${property.country}`}
              </span>
            </div>
          </div>
        </div>
        <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center gap-2 transition-colors">
          <Edit className="h-4 w-4" />
          Edit Property
        </button>
      </div>

      {/* Property Details Cards */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Info Card */}
        <div className="lg:col-span-2 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Property Information</h3>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">Total Units</label>
                <p className="mt-1 text-sm text-gray-900">{property.units_count}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(property.created_at)}</p>
              </div>

              {property.description && (
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="mt-1 text-sm text-gray-900">{property.description}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Stats Card */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Statistics</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">Occupied Units</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {units.filter(u => u.status === 'occupied').length} / {property.units_count}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">Vacant Units</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {units.filter(u => u.status === 'vacant').length}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Wrench className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">Maintenance Requests</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {maintenanceRequests.filter(r => r.status !== 'resolved').length}
                </span>
              </div>
            </div>

            {/* Occupancy Rate */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Occupancy Rate</span>
                <span className="text-sm text-gray-500">
                  {property.units_count > 0 
                    ? Math.round((units.filter(u => u.status === 'occupied').length / property.units_count) * 100)
                    : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-indigo-600 h-2 rounded-full" 
                  style={{ 
                    width: `${property.units_count > 0 
                      ? (units.filter(u => u.status === 'occupied').length / property.units_count) * 100
                      : 0}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Units Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Units</h3>
            <button 
              onClick={() => setIsUnitModalOpen(true)}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center gap-2 transition-colors text-sm"
            >
              <Plus className="h-4 w-4" />
              Add Unit
            </button>
          </div>

          {units.length === 0 ? (
            <div className="text-center py-8">
              <Building className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No units</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start by adding units to this property.
              </p>
              <div className="mt-6">
                <button 
                  onClick={() => setIsUnitModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <Plus className="-ml-1 mr-2 h-5 w-5" />
                  Add First Unit
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {units.map((unit) => (
                <div key={unit.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">{unit.name}</h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>
                      {unit.status}
                    </span>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    KSh {unit.rent_amount.toLocaleString()}
                    <span className="text-sm text-gray-500 font-normal">/month</span>
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Added {formatDate(unit.created_at)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Maintenance Requests Section */}
      {maintenanceRequests.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Maintenance Requests</h3>
              <button 
                onClick={() => router.push('/dashboard/maintenance')}
                className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                View All
              </button>
            </div>
            <MaintenanceTable
              requests={maintenanceRequests.slice(0, 5)}
              onRequestUpdated={() => {
                // Refresh maintenance requests
                if (property && units.length > 0) {
                  const fetchMaintenance = async () => {
                    const maintenancePromises = units.map(unit => 
                      maintenanceApi.getRequestsByUnit(unit.id)
                    );
                    const maintenanceResponses = await Promise.all(maintenancePromises);
                    const allMaintenanceRequests: MaintenanceRequest[] = [];
                    
                    maintenanceResponses.forEach(response => {
                      if (response.success && response.data) {
                        allMaintenanceRequests.push(...(response.data as MaintenanceRequest[]));
                      }
                    });
                    
                    setMaintenanceRequests(allMaintenanceRequests);
                  };
                  fetchMaintenance();
                }
              }}
              showActions={false}
            />
          </div>
        </div>
      )}

      {/* Unit Modal */}
      {property && (
        <UnitModal 
          isOpen={isUnitModalOpen}
          onClose={() => setIsUnitModalOpen(false)}
          onUnitCreated={handleUnitCreated}
          propertyId={property.id}
        />
      )}
    </div>
  );
}