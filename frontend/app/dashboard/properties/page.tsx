'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { propertiesApi } from '../../lib/api';
import { Building, MapPin, Users, DollarSign, Plus, Search, Eye } from 'lucide-react';
import PropertyModal from '../../components/PropertyModal';

interface Property {
  id: string;
  owner_id: string;
  title: string;
  address: string;
  city?: string;
  country?: string;
  description?: string;
  units_count: number;
  created_at: string;
  updated_at: string;
}

interface Unit {
  id: string;
  property_id: string;
  name: string;
  rent_amount: number;
  status: 'vacant' | 'occupied' | 'maintenance';
  created_at: string;
  updated_at: string;
}

interface PropertyWithStats extends Property {
  monthly_income: number;
  occupied_units: number;
}

export default function PropertiesPage() {
  const router = useRouter();
  const [properties, setProperties] = useState<PropertyWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const fetchPropertiesWithStats = async (): Promise<PropertyWithStats[]> => {
    const response = await propertiesApi.getProperties();
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch properties');
    }

    const propertiesData = (response.data as Property[]) || [];
    
    // Fetch units for each property to calculate monthly income
    const propertiesWithStats = await Promise.all(
      propertiesData.map(async (property) => {
        try {
          const unitsResponse = await propertiesApi.getPropertyUnits(property.id);
          let monthly_income = 0;
          let occupied_units = 0;
          
          if (unitsResponse.success && unitsResponse.data) {
            const units = unitsResponse.data as Unit[];
            monthly_income = units.reduce((total, unit) => total + unit.rent_amount, 0);
            occupied_units = units.filter(unit => unit.status === 'occupied').length;
          }
          
          return {
            ...property,
            monthly_income,
            occupied_units
          } as PropertyWithStats;
        } catch (error) {
          console.error(`Failed to fetch units for property ${property.id}:`, error);
          return {
            ...property,
            monthly_income: 0,
            occupied_units: 0
          } as PropertyWithStats;
        }
      })
    );

    return propertiesWithStats;
  };

  useEffect(() => {
    const fetchProperties = async () => {
      try {
        console.log('Fetching properties...');
        const propertiesWithStats = await fetchPropertiesWithStats();
        console.log('Setting properties with stats:', propertiesWithStats);
        setProperties(propertiesWithStats);
      } catch (error) {
        console.error('Failed to fetch properties:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, []);

  const handlePropertyCreated = () => {
    // Refresh properties list after creating a new property
    const refreshProperties = async () => {
      try {
        const propertiesWithStats = await fetchPropertiesWithStats();
        setProperties(propertiesWithStats);
      } catch (error) {
        console.error('Failed to refresh properties:', error);
      }
    };

    refreshProperties();
  };

  const filteredProperties = properties.filter(property =>
    property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (property.city && property.city.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Building className="h-8 w-8 text-indigo-600" />
          <h1 className="text-2xl font-bold text-gray-900">Properties</h1>
        </div>
        <button
          onClick={() => setIsModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Property
        </button>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search properties..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>

      {/* Properties Grid */}
      {filteredProperties.length === 0 ? (
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No properties</h3>
          <p className="mt-1 text-sm text-gray-500">
            {properties.length === 0 
              ? "Get started by adding your first property."
              : "No properties match your search."
            }
          </p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setIsModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" />
              Add Property
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredProperties.map((property) => (
            <div key={property.id} className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              {/* Property Image Placeholder */}
              <div className="h-48 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                <Building className="h-16 w-16 text-white opacity-50" />
              </div>
              
              {/* Property Info */}
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {property.title}
                    </h3>
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                      <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                      <span className="truncate">
                        {property.address}
                        {property.city && `, ${property.city}`}
                      </span>
                    </div>
                  </div>
                  <button 
                    onClick={() => router.push(`/dashboard/properties/${property.id}`)}
                    className="ml-2 p-2 text-gray-400 hover:text-indigo-600 transition-colors"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                </div>

                {/* Property Stats */}
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {property.occupied_units}/{property.units_count}
                      </p>
                      <p className="text-xs text-gray-500">Occupied</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-green-500 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        KSh {property.monthly_income.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">Monthly</p>
                    </div>
                  </div>
                </div>

                {/* Occupancy Bar */}
                {property.units_count > 0 && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-gray-700">Occupancy</span>
                      <span className="text-xs text-gray-500">
                        {Math.round((property.occupied_units / property.units_count) * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-indigo-600 h-2 rounded-full transition-all duration-300" 
                        style={{ 
                          width: `${(property.occupied_units / property.units_count) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Property Description */}
                {property.description && (
                  <p className="mt-3 text-sm text-gray-600 line-clamp-2">
                    {property.description}
                  </p>
                )}

                {/* Action Button */}
                <div className="mt-4">
                  <button 
                    onClick={() => router.push(`/dashboard/properties/${property.id}`)}
                    className="w-full bg-gray-50 hover:bg-gray-100 text-gray-700 py-2 px-4 rounded-md text-sm font-medium transition-colors"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Property Modal */}
      <PropertyModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onPropertyCreated={handlePropertyCreated}
      />
    </div>
  );
}