'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { dashboardApi, maintenanceApi, paymentsApi } from '../lib/api';
import { DashboardStats } from '../types/dashboard';
import { MaintenanceRequest, MaintenanceSummary } from '../types/maintenance';
import { Payment, PaymentSummary } from '../types/payment';
import { Building, Home, Users, DollarSign, Wrench, AlertTriangle, TrendingUp } from 'lucide-react';
import MaintenanceTable from '../components/MaintenanceTable';
import PaymentTable from '../components/PaymentTable';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [maintenanceRequests, setMaintenanceRequests] = useState<MaintenanceRequest[]>([]);
  const [maintenanceSummary, setMaintenanceSummary] = useState<MaintenanceSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching dashboard data...');
        const [statsResponse, maintenanceResponse, maintenanceSummaryResponse] = await Promise.all([
          dashboardApi.getStats(),
          maintenanceApi.getRequests({ status: 'pending' }),
          maintenanceApi.getSummary()
        ]);

        if (statsResponse.success && statsResponse.data) {
          console.log('Setting stats data:', statsResponse.data);
          setStats(statsResponse.data as DashboardStats);
        } else {
          console.error('Failed to fetch dashboard stats:', statsResponse.message);
        }

        if (maintenanceResponse.success && maintenanceResponse.data) {
          setMaintenanceRequests((maintenanceResponse.data as MaintenanceRequest[]).slice(0, 5));
        }

        if (maintenanceSummaryResponse.success && maintenanceSummaryResponse.data) {
          setMaintenanceSummary(maintenanceSummaryResponse.data as MaintenanceSummary);
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const statsCards = [
    {
      name: 'Total Properties',
      value: stats?.total_properties || 0,
      icon: Building,
      color: 'bg-blue-500',
    },
    {
      name: 'Total Units',
      value: stats?.total_units || 0,
      icon: Home,
      color: 'bg-green-500',
    },
    {
      name: 'Occupied Units',
      value: stats?.occupied_units || 0,
      icon: Users,
      color: 'bg-purple-500',
    },
    {
      name: 'Monthly Rent',
      value: `KSh ${(stats?.total_monthly_rent || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
    },
  ];

  const maintenanceCards = maintenanceSummary ? [
    {
      name: 'Pending Requests',
      value: maintenanceSummary.pending_count,
      icon: Wrench,
      color: 'bg-orange-500',
    },
    {
      name: 'Urgent Issues',
      value: maintenanceSummary.urgent_count + maintenanceSummary.high_count,
      icon: AlertTriangle,
      color: 'bg-red-500',
    },
  ] : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user?.name}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {[...statsCards, ...maintenanceCards].map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Stats */}
      {stats && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Occupancy Rate</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-green-600">
                  {stats.total_units > 0 
                    ? Math.round((stats.occupied_units / stats.total_units) * 100)
                    : 0}%
                </div>
                <p className="text-sm text-gray-500">
                  {stats.occupied_units} of {stats.total_units} units occupied
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Active Leases</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-blue-600">
                  {stats.active_leases}
                </div>
                <p className="text-sm text-gray-500">Currently active</p>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Vacant Units</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-yellow-600">
                  {stats.vacant_units}
                </div>
                <p className="text-sm text-gray-500">Available for rent</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pending Maintenance Requests */}
      {maintenanceRequests.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Pending Maintenance Requests</h3>
              <button 
                onClick={() => router.push('/dashboard/maintenance')}
                className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                View All
              </button>
            </div>
            <MaintenanceTable
              requests={maintenanceRequests}
              onRequestUpdated={async () => {
                // Refresh maintenance data
                try {
                  const [maintenanceResponse, maintenanceSummaryResponse] = await Promise.all([
                    maintenanceApi.getRequests({ status: 'pending' }),
                    maintenanceApi.getSummary()
                  ]);

                  if (maintenanceResponse.success && maintenanceResponse.data) {
                    setMaintenanceRequests((maintenanceResponse.data as MaintenanceRequest[]).slice(0, 5));
                  }

                  if (maintenanceSummaryResponse.success && maintenanceSummaryResponse.data) {
                    setMaintenanceSummary(maintenanceSummaryResponse.data as MaintenanceSummary);
                  }
                } catch (error) {
                  console.error('Failed to refresh maintenance data:', error);
                }
              }}
              showActions={false}
            />
          </div>
        </div>
      )}
    </div>
  );
}