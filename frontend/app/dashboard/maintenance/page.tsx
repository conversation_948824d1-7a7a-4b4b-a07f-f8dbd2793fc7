'use client';

import { useState, useEffect } from 'react';
import { maintenanceApi } from '../../lib/api';
import { MaintenanceRequest, MaintenanceSummary } from '../../types/maintenance';
import MaintenanceTable from '../../components/MaintenanceTable';
import MaintenanceModal from '../../components/MaintenanceModal';
import MaintenanceDetailModal from '../../components/MaintenanceDetailModal';
import { 
  Plus, 
  Filter, 
  Wrench, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  DollarSign 
} from 'lucide-react';

export default function MaintenancePage() {
  const [requests, setRequests] = useState<MaintenanceRequest[]>([]);
  const [summary, setSummary] = useState<MaintenanceSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<MaintenanceRequest | null>(null);
  const [editingRequest, setEditingRequest] = useState<MaintenanceRequest | null>(null);
  
  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');

  useEffect(() => {
    fetchData();
  }, [statusFilter]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [requestsResponse, summaryResponse] = await Promise.all([
        maintenanceApi.getRequests(statusFilter ? { status: statusFilter } : {}),
        maintenanceApi.getSummary()
      ]);

      if (requestsResponse.success) {
        let filteredRequests = (requestsResponse.data as MaintenanceRequest[]) || [];

        // Apply priority filter if set
        if (priorityFilter) {
          filteredRequests = filteredRequests.filter(r => r.priority === priorityFilter);
        }

        setRequests(filteredRequests);
      } else {
        setError('Failed to fetch maintenance requests');
      }

      if (summaryResponse.success && summaryResponse.data) {
        setSummary(summaryResponse.data as MaintenanceSummary);
      }
    } catch (err) {
      console.error('Error fetching maintenance data:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestSelected = (request: MaintenanceRequest) => {
    setSelectedRequest(request);
    setShowDetailModal(true);
  };

  const handleEdit = (request: MaintenanceRequest) => {
    setEditingRequest(request);
    setShowCreateModal(true);
    setShowDetailModal(false);
  };

  const handleModalClose = () => {
    setShowCreateModal(false);
    setShowDetailModal(false);
    setSelectedRequest(null);
    setEditingRequest(null);
  };

  const handleSuccess = () => {
    fetchData();
    handleModalClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const summaryCards = summary ? [
    {
      name: 'Pending',
      value: summary.pending_count,
      icon: Clock,
      color: 'bg-yellow-500',
    },
    {
      name: 'In Progress',
      value: summary.in_progress_count,
      icon: Wrench,
      color: 'bg-blue-500',
    },
    {
      name: 'Resolved',
      value: summary.resolved_count,
      icon: CheckCircle,
      color: 'bg-green-500',
    },
    {
      name: 'Urgent Items',
      value: summary.urgent_count + summary.high_count,
      icon: AlertTriangle,
      color: 'bg-red-500',
    },
    {
      name: 'Total Cost',
      value: formatCurrency(summary.total_cost),
      icon: DollarSign,
      color: 'bg-purple-500',
    },
  ] : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Wrench className="h-8 w-8 text-indigo-600" />
          <h1 className="text-2xl font-bold text-gray-900">Maintenance Requests</h1>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Request
        </button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
          {summaryCards.map((card) => {
            const Icon = card.icon;
            return (
              <div key={card.name} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className={`${card.color} p-3 rounded-md`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          {card.name}
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {card.value}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <Filter className="h-5 w-5 text-gray-400 mr-2" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <div className="flex space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="cancelled">Cancelled</option>
            </select>

            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            >
              <option value="">All Priority</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            {(statusFilter || priorityFilter) && (
              <button
                onClick={() => {
                  setStatusFilter('');
                  setPriorityFilter('');
                }}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Maintenance Table */}
      <div className="bg-white shadow rounded-lg">
        {requests.length === 0 && (statusFilter || priorityFilter) ? (
          <div className="text-center py-12">
            <Wrench className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance requests</h3>
            <p className="mt-1 text-sm text-gray-500">
              No maintenance requests match your current filters.
            </p>
            <div className="mt-6">
              <button
                onClick={() => {
                  setStatusFilter('');
                  setPriorityFilter('');
                }}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        ) : (
          <MaintenanceTable
            requests={requests}
            onRequestUpdated={fetchData}
            onRequestSelected={handleRequestSelected}
            showActions={true}
            onAddNew={() => setShowCreateModal(true)}
          />
        )}
      </div>

      {/* Modals */}
      <MaintenanceModal
        isOpen={showCreateModal}
        onClose={handleModalClose}
        onSuccess={handleSuccess}
        request={editingRequest || undefined}
      />

      <MaintenanceDetailModal
        isOpen={showDetailModal}
        onClose={handleModalClose}
        onEdit={handleEdit}
        request={selectedRequest}
      />
    </div>
  );
}