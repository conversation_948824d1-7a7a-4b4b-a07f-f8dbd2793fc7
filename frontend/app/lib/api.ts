const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/v1';

interface ApiResponse<T> {
  data?: T;
  success: boolean;
  message?: string;
}

// Global token storage - this will be set by the auth context
let globalAccessToken: string | null = null;

export function setGlobalAccessToken(token: string | null) {
  globalAccessToken = token;
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    // Add Authorization header if token exists
    if (globalAccessToken) {
      headers.Authorization = `Bearer ${globalAccessToken}`;
      console.log('Using access token for API request:', endpoint);
    } else {
      console.log('No access token available for API request:', endpoint);
    }

    console.log('API request headers:', headers);

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers,
      credentials: 'include',
      ...options,
    });

    if (!response.ok) {
      return {
        success: false,
        message: `HTTP error! status: ${response.status}`,
      };
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data,
      message: data.message,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export const dashboardApi = {
  getStats: () => apiRequest('/dashboard/stats'),
  getSummary: () => apiRequest('/dashboard/summary'),
};

export const propertiesApi = {
  getProperties: () => apiRequest('/properties'),
  createProperty: (data: unknown) => apiRequest('/properties', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getProperty: (id: string) => apiRequest(`/properties/${id}`),
  createUnit: (propertyId: string, data: unknown) => apiRequest(`/properties/${propertyId}/units`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getPropertyUnits: (propertyId: string) => apiRequest(`/properties/${propertyId}/units`),
};

export const leasesApi = {
  getLeases: () => apiRequest('/leases'),
  createLease: (data: unknown) => apiRequest('/leases', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getLease: (id: string) => apiRequest(`/leases/${id}`),
  updateLease: (id: string, data: unknown) => apiRequest(`/leases/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deactivateLease: (id: string) => apiRequest(`/leases/${id}/deactivate`, {
    method: 'PUT',
  }),
};

export const tenantsApi = {
  getTenants: () => apiRequest('/tenants'),
  createTenant: (data: unknown) => apiRequest('/tenants', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getTenant: (id: string) => apiRequest(`/tenants/${id}`),
  updateTenant: (id: string, data: unknown) => apiRequest(`/tenants/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteTenant: (id: string) => apiRequest(`/tenants/${id}`, {
    method: 'DELETE',
  }),
};

export const maintenanceApi = {
  getRequests: (params?: { status?: string; unit_id?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.status) searchParams.append('status', params.status);
    if (params?.unit_id) searchParams.append('unit_id', params.unit_id);
    const queryString = searchParams.toString();
    return apiRequest(`/maintenance${queryString ? `?${queryString}` : ''}`);
  },
  createRequest: (data: unknown) => apiRequest('/maintenance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getRequest: (id: string) => apiRequest(`/maintenance/${id}`),
  updateRequest: (id: string, data: unknown) => apiRequest(`/maintenance/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteRequest: (id: string) => apiRequest(`/maintenance/${id}`, {
    method: 'DELETE',
  }),
  getPendingRequests: () => apiRequest('/maintenance/pending'),
  getActiveRequests: () => apiRequest('/maintenance/active'),
  getSummary: () => apiRequest('/maintenance/summary'),
  getRequestsByUnit: (unitId: string) => apiRequest(`/maintenance/unit/${unitId}`),
  createAttachment: (requestId: string, data: unknown) => apiRequest(`/maintenance/${requestId}/attachments`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getAttachments: (requestId: string) => apiRequest(`/maintenance/${requestId}/attachments`),
};

export const paymentsApi = {
  getPayments: (params?: { lease_id?: string; start_date?: string; end_date?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.lease_id) searchParams.append('lease_id', params.lease_id);
    if (params?.start_date) searchParams.append('start_date', params.start_date);
    if (params?.end_date) searchParams.append('end_date', params.end_date);
    const queryString = searchParams.toString();
    return apiRequest(`/payments${queryString ? `?${queryString}` : ''}`);
  },
  createPayment: (data: unknown) => apiRequest('/payments', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getPayment: (id: string) => apiRequest(`/payments/${id}`),
  updatePayment: (id: string, data: unknown) => apiRequest(`/payments/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deletePayment: (id: string) => apiRequest(`/payments/${id}`, {
    method: 'DELETE',
  }),
  getPaymentsByLease: (leaseId: string) => apiRequest(`/payments/lease/${leaseId}`),
  getSummary: (params?: { start_date?: string; end_date?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.start_date) searchParams.append('start_date', params.start_date);
    if (params?.end_date) searchParams.append('end_date', params.end_date);
    const queryString = searchParams.toString();
    return apiRequest(`/payments/summary${queryString ? `?${queryString}` : ''}`);
  },
};

export const profileApi = {
  getProfile: () => apiRequest('/profile'),
  updateProfile: (data: { name: string }) => apiRequest('/profile', {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  changePassword: (data: { current_password: string; new_password: string }) => apiRequest('/profile/change-password', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
};

export default apiRequest;