export type PaymentMethod = 'manual' | 'mpesa' | 'card' | 'bank_transfer' | 'cash' | 'check';

export interface Payment {
  id: string;
  lease_id: string;
  amount: number;
  paid_at: string;
  payment_method?: PaymentMethod;
  notes?: string;
  created_by?: string;
  created_at: string;
  
  // Joined fields from backend
  lease_rent_amount?: number;
  unit_name?: string;
  tenant_name?: string;
}

export interface CreatePaymentRequest {
  lease_id: string;
  amount: number;
  paid_at: string;
  payment_method?: PaymentMethod;
  notes?: string;
}

export interface UpdatePaymentRequest {
  amount: number;
  paid_at: string;
  payment_method?: PaymentMethod;
  notes?: string;
}

export interface PaymentSummary {
  total_amount: number;
  payment_count: number;
  period_start: string;
  period_end: string;
}

// Payment method display configurations
export const PAYMENT_METHOD_CONFIG = {
  manual: {
    label: 'Manual',
    color: 'bg-gray-100 text-gray-800',
  },
  mpesa: {
    label: 'M-Pesa',
    color: 'bg-green-100 text-green-800',
  },
  card: {
    label: 'Card',
    color: 'bg-blue-100 text-blue-800',
  },
  bank_transfer: {
    label: 'Bank Transfer',
    color: 'bg-indigo-100 text-indigo-800',
  },
  cash: {
    label: 'Cash',
    color: 'bg-yellow-100 text-yellow-800',
  },
  check: {
    label: 'Check',
    color: 'bg-purple-100 text-purple-800',
  },
} as const;