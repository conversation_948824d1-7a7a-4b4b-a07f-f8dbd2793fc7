export type MaintenanceStatus = 'pending' | 'in_progress' | 'resolved' | 'cancelled';
export type MaintenancePriority = 'low' | 'medium' | 'high' | 'urgent';

export interface MaintenanceRequest {
  id: string;
  unit_id?: string;
  lease_id?: string;
  title: string;
  description?: string;
  status: MaintenanceStatus;
  priority: MaintenancePriority;
  created_by?: string;
  assigned_to?: string;
  cost: number;
  created_at: string;
  resolved_at?: string;
  
  // Joined fields
  unit_name?: string;
  property_id?: string;
  property_title?: string;
  tenant_name?: string;
  tenant_email?: string;
  lease_active?: boolean;
}

export interface CreateMaintenanceRequest {
  unit_id?: string;
  lease_id?: string;
  title: string;
  description?: string;
  priority: MaintenancePriority;
}

export interface UpdateMaintenanceRequest {
  status?: MaintenanceStatus;
  assigned_to?: string;
  cost?: number;
  description?: string;
}

export interface MaintenanceSummary {
  pending_count: number;
  in_progress_count: number;
  resolved_count: number;
  urgent_count: number;
  high_count: number;
  total_cost: number;
  avg_resolution_days?: number;
}

export interface Attachment {
  id: string;
  parent_type: string;
  parent_id: string;
  url: string;
  filename?: string;
  file_size?: number;
  content_type?: string;
  uploaded_by?: string;
  uploaded_at: string;
}

export interface CreateAttachment {
  url: string;
  filename?: string;
  file_size?: number;
  content_type?: string;
}

// Status and priority display configurations
export const MAINTENANCE_STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800',
  },
  in_progress: {
    label: 'In Progress',
    color: 'bg-blue-100 text-blue-800',
  },
  resolved: {
    label: 'Resolved',
    color: 'bg-green-100 text-green-800',
  },
  cancelled: {
    label: 'Cancelled',
    color: 'bg-gray-100 text-gray-800',
  },
} as const;

export const MAINTENANCE_PRIORITY_CONFIG = {
  low: {
    label: 'Low',
    color: 'bg-green-100 text-green-800',
    order: 4,
  },
  medium: {
    label: 'Medium',
    color: 'bg-yellow-100 text-yellow-800',
    order: 3,
  },
  high: {
    label: 'High',
    color: 'bg-orange-100 text-orange-800',
    order: 2,
  },
  urgent: {
    label: 'Urgent',
    color: 'bg-red-100 text-red-800',
    order: 1,
  },
} as const;