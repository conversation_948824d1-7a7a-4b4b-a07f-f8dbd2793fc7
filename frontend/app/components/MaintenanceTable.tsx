'use client';

import { useState } from 'react';
import { 
  MaintenanceRequest, 
  MAIN<PERSON>NANCE_STATUS_CONFIG, 
  MAINTENANCE_PRIORITY_CONFIG,
  MaintenanceStatus,
  MaintenancePriority 
} from '../types/maintenance';
import {
  Wrench,
  Calendar,
  DollarSign,
  User,
  Building,
  Eye,
  Edit,
  Trash2,
  Clock,
  Plus
} from 'lucide-react';
import { maintenanceApi } from '../lib/api';

interface MaintenanceTableProps {
  requests: MaintenanceRequest[];
  onRequestUpdated?: () => void;
  onRequestSelected?: (request: MaintenanceRequest) => void;
  showActions?: boolean;
  onAddNew?: () => void;
}

export default function MaintenanceTable({
  requests,
  onRequestUpdated,
  onRequestSelected,
  showActions = true,
  onAddNew
}: MaintenanceTableProps) {
  const [updatingRequest, setUpdatingRequest] = useState<string | null>(null);

  const handleStatusUpdate = async (requestId: string, newStatus: MaintenanceStatus) => {
    if (updatingRequest) return;
    
    setUpdatingRequest(requestId);
    try {
      const response = await maintenanceApi.updateRequest(requestId, { status: newStatus });
      if (response.success) {
        onRequestUpdated?.();
      } else {
        alert('Failed to update status: ' + response.message);
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update status');
    } finally {
      setUpdatingRequest(null);
    }
  };

  const handleDelete = async (requestId: string) => {
    if (!confirm('Are you sure you want to delete this maintenance request?')) return;
    
    try {
      const response = await maintenanceApi.deleteRequest(requestId);
      if (response.success) {
        onRequestUpdated?.();
      } else {
        alert('Failed to delete request: ' + response.message);
      }
    } catch (error) {
      console.error('Error deleting request:', error);
      alert('Failed to delete request');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status: MaintenanceStatus) => {
    const config = MAINTENANCE_STATUS_CONFIG[status];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getPriorityBadge = (priority: MaintenancePriority) => {
    const config = MAINTENANCE_PRIORITY_CONFIG[priority];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (requests.length === 0) {
    return (
      <div className="text-center py-12">
        <Wrench className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance requests</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by creating a new maintenance request.</p>
        {onAddNew && (
          <div className="mt-6">
            <button
              type="button"
              onClick={onAddNew}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" />
              New Request
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Request Details
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Property/Unit
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Priority
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Assignment
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Cost
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
            {showActions && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {requests.map((request) => (
            <tr key={request.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col">
                  <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                    {request.title}
                  </div>
                  {request.description && (
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {request.description}
                    </div>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Building className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900">
                      {request.property_title || 'Unknown Property'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {request.unit_name || 'No Unit'}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col space-y-1">
                  {getStatusBadge(request.status)}
                  {request.status === 'in_progress' && (
                    <button
                      onClick={() => handleStatusUpdate(request.id, 'resolved')}
                      disabled={updatingRequest === request.id}
                      className="text-xs text-green-600 hover:text-green-800 disabled:opacity-50"
                    >
                      {updatingRequest === request.id ? 'Updating...' : 'Mark Resolved'}
                    </button>
                  )}
                  {request.status === 'pending' && (
                    <button
                      onClick={() => handleStatusUpdate(request.id, 'in_progress')}
                      disabled={updatingRequest === request.id}
                      className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
                    >
                      {updatingRequest === request.id ? 'Updating...' : 'Start Work'}
                    </button>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getPriorityBadge(request.priority)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="text-sm text-gray-900">
                    {request.assigned_to || (
                      <span className="text-gray-500 italic">Unassigned</span>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                  <div className="text-sm text-gray-900">
                    {request.cost > 0 ? formatCurrency(request.cost) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col">
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    {formatDate(request.created_at)}
                  </div>
                  {request.resolved_at && (
                    <div className="flex items-center text-sm text-green-600 mt-1">
                      <Clock className="h-4 w-4 mr-1" />
                      Resolved {formatDate(request.resolved_at)}
                    </div>
                  )}
                </div>
              </td>
              {showActions && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onRequestSelected?.(request)}
                      className="text-indigo-600 hover:text-indigo-900"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onRequestSelected?.(request)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Edit Request"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(request.id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete Request"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}