'use client';

import { useState } from 'react';
import { 
  X, 
  DollarSign, 
  Calendar, 
  CreditCard,
  User,
  Building,
  FileText,
  Edit
} from 'lucide-react';
import { 
  Payment, 
  PAYMENT_METHOD_CONFIG 
} from '../types/payment';

interface PaymentDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (payment: Payment) => void;
  payment: Payment | null;
}

export default function PaymentDetailModal({ 
  isOpen, 
  onClose, 
  onEdit,
  payment 
}: PaymentDetailModalProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getPaymentMethodDisplay = (method?: string) => {
    if (!method) return { label: 'Not specified', color: 'bg-gray-100 text-gray-800', icon: '❓' };
    const config = PAYMENT_METHOD_CONFIG[method as keyof typeof PAYMENT_METHOD_CONFIG];
    return config || { label: method, color: 'bg-gray-100 text-gray-800', icon: '❓' };
  };

  if (!isOpen || !payment) return null;

  const methodDisplay = getPaymentMethodDisplay(payment.payment_method);

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Payment Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Payment Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Payment #{payment.id.slice(-8)}
              </h3>
              <div className="text-2xl font-bold text-green-600 mb-2">
                {formatCurrency(payment.amount)}
              </div>
              {payment.lease_rent_amount && payment.lease_rent_amount !== payment.amount && (
                <p className="text-sm text-gray-500">
                  Partial payment of {formatCurrency(payment.lease_rent_amount)} monthly rent
                </p>
              )}
            </div>
            <button
              onClick={() => onEdit(payment)}
              className="ml-4 px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
            >
              <Edit className="h-4 w-4 inline mr-1" />
              Edit
            </button>
          </div>

          {/* Payment Method and Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Payment Method
              </label>
              <div className="flex items-center">
                <CreditCard className="h-5 w-5 text-gray-400 mr-2" />
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${methodDisplay.color}`}>
                  {methodDisplay.label}
                </span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Payment Date
              </label>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-900">
                  {formatDate(payment.paid_at)}
                </span>
              </div>
            </div>
          </div>

          {/* Tenant and Unit Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Lease Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <User className="h-5 w-5 text-gray-400 mr-2" />
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {payment.tenant_name || 'Unknown Tenant'}
                  </div>
                  <div className="text-sm text-gray-500">
                    Tenant
                  </div>
                </div>
              </div>
              <div className="flex items-center">
                <Building className="h-5 w-5 text-gray-400 mr-2" />
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {payment.unit_name || 'Unknown Unit'}
                  </div>
                  <div className="text-sm text-gray-500">
                    Unit
                  </div>
                </div>
              </div>
            </div>
            
            {payment.lease_rent_amount && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Monthly Rent</span>
                  <span className="text-sm text-gray-900">{formatCurrency(payment.lease_rent_amount)}</span>
                </div>
              </div>
            )}
          </div>

          {/* Notes */}
          {payment.notes && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-2">
                Notes
              </label>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start">
                  <FileText className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-gray-900">{payment.notes}</p>
                </div>
              </div>
            </div>
          )}

          {/* Timeline */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Timeline</h4>
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-600">Payment Date:</span>
                <span className="ml-2 text-gray-900">{formatDate(payment.paid_at)}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-600">Recorded:</span>
                <span className="ml-2 text-gray-900">{formatDate(payment.created_at)}</span>
              </div>
              {payment.created_by && (
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Recorded by:</span>
                  <span className="ml-2 text-gray-900">{payment.created_by}</span>
                </div>
              )}
            </div>
          </div>

          {/* Payment Details */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-3">Payment Summary</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-700">Payment ID</span>
                <span className="text-sm font-mono text-blue-900">#{payment.id}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-700">Lease ID</span>
                <span className="text-sm font-mono text-blue-900">#{payment.lease_id}</span>
              </div>
              <div className="flex justify-between items-center font-medium">
                <span className="text-sm text-blue-700">Total Amount</span>
                <span className="text-lg text-blue-900">{formatCurrency(payment.amount)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Close
          </button>
          <button
            onClick={() => onEdit(payment)}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
          >
            Edit Payment
          </button>
        </div>
      </div>
    </div>
  );
}