'use client';

import { useState, useEffect } from 'react';
import { X, Building, User, DollarSign, Calendar } from 'lucide-react';
import { 
  MaintenanceRequest, 
  CreateMaintenanceRequest, 
  UpdateMaintenanceRequest,
  MaintenancePriority,
  MaintenanceStatus,
  MAINTENANCE_PRIORITY_CONFIG,
  MAINTENANCE_STATUS_CONFIG
} from '../types/maintenance';
import { maintenanceApi, propertiesApi } from '../lib/api';

interface Property {
  id: string;
  title: string;
  address: string;
}

interface Unit {
  id: string;
  name: string;
  property_id: string;
}

interface MaintenanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  request?: MaintenanceRequest; // If provided, this is an edit modal
}

export default function MaintenanceModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  request 
}: MaintenanceModalProps) {
  const [formData, setFormData] = useState<CreateMaintenanceRequest & UpdateMaintenanceRequest>({
    title: '',
    description: '',
    priority: 'medium',
    unit_id: undefined,
    lease_id: undefined,
    status: undefined,
    assigned_to: undefined,
    cost: undefined,
  });
  
  const [properties, setProperties] = useState<Property[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditMode = Boolean(request);

  useEffect(() => {
    if (isOpen) {
      fetchProperties();
      if (request) {
        // Pre-fill form for editing
        setFormData({
          title: request.title,
          description: request.description || '',
          priority: request.priority,
          unit_id: request.unit_id,
          lease_id: request.lease_id,
          status: request.status,
          assigned_to: request.assigned_to || '',
          cost: request.cost > 0 ? request.cost : undefined,
        });
        if (request.property_id) {
          setSelectedPropertyId(request.property_id);
        }
      } else {
        // Reset form for new request
        setFormData({
          title: '',
          description: '',
          priority: 'medium',
          unit_id: undefined,
          lease_id: undefined,
          status: undefined,
          assigned_to: undefined,
          cost: undefined,
        });
        setSelectedPropertyId('');
      }
    }
  }, [isOpen, request]);

  useEffect(() => {
    if (selectedPropertyId) {
      fetchUnits(selectedPropertyId);
    } else {
      setUnits([]);
    }
  }, [selectedPropertyId]);

  const fetchProperties = async () => {
    try {
      const response = await propertiesApi.getProperties();
      if (response.success && response.data) {
        setProperties(response.data as Property[]);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
    }
  };

  const fetchUnits = async (propertyId: string) => {
    try {
      const response = await propertiesApi.getPropertyUnits(propertyId);
      if (response.success && response.data) {
        setUnits(response.data as Unit[]);
      }
    } catch (error) {
      console.error('Error fetching units:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      let response;
      
      if (isEditMode && request) {
        // Update existing request
        const updateData: UpdateMaintenanceRequest = {};
        if (formData.status !== request.status) updateData.status = formData.status;
        if (formData.assigned_to !== request.assigned_to) updateData.assigned_to = formData.assigned_to || undefined;
        if (formData.cost !== request.cost) updateData.cost = formData.cost;
        if (formData.description !== request.description) updateData.description = formData.description || undefined;
        
        response = await maintenanceApi.updateRequest(request.id, updateData);
      } else {
        // Create new request
        const createData: CreateMaintenanceRequest = {
          title: formData.title,
          description: formData.description || undefined,
          priority: formData.priority,
        };
        
        if (formData.unit_id) {
          createData.unit_id = formData.unit_id;
        }
        // Note: lease_id support would require additional UI
        
        response = await maintenanceApi.createRequest(createData);
      }

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.message || 'Failed to save maintenance request');
      }
    } catch (error) {
      console.error('Error saving maintenance request:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {isEditMode ? 'Update Maintenance Request' : 'New Maintenance Request'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Brief description of the issue"
              disabled={isEditMode} // Don't allow editing title
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Detailed description of the maintenance issue"
            />
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: e.target.value as MaintenancePriority })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isEditMode} // Don't allow editing priority
            >
              {Object.entries(MAINTENANCE_PRIORITY_CONFIG).map(([value, config]) => (
                <option key={value} value={value}>
                  {config.label}
                </option>
              ))}
            </select>
          </div>

          {/* Property and Unit Selection (only for new requests) */}
          {!isEditMode && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Property *
                </label>
                <div className="relative">
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <select
                    required
                    value={selectedPropertyId}
                    onChange={(e) => setSelectedPropertyId(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a property</option>
                    {properties.map((property) => (
                      <option key={property.id} value={property.id}>
                        {property.title} - {property.address}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit
                </label>
                <select
                  value={formData.unit_id || ''}
                  onChange={(e) => setFormData({ ...formData, unit_id: e.target.value || undefined })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={!selectedPropertyId}
                >
                  <option value="">Select a unit (optional)</option>
                  {units.map((unit) => (
                    <option key={unit.id} value={unit.id}>
                      {unit.name}
                    </option>
                  ))}
                </select>
              </div>
            </>
          )}

          {/* Edit Mode Fields */}
          {isEditMode && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.status || ''}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as MaintenanceStatus })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {Object.entries(MAINTENANCE_STATUS_CONFIG).map(([value, config]) => (
                    <option key={value} value={value}>
                      {config.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned To
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={formData.assigned_to || ''}
                    onChange={(e) => setFormData({ ...formData, assigned_to: e.target.value })}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Technician or contractor name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cost
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.cost || ''}
                    onChange={(e) => setFormData({ ...formData, cost: e.target.value ? parseFloat(e.target.value) : undefined })}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>
              </div>
            </>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !formData.title.trim() || !formData.priority}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Request' : 'Create Request'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}