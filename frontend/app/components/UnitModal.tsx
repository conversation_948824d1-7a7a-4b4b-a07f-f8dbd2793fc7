'use client';

import { useState } from 'react';
import { X } from 'lucide-react';
import { propertiesApi } from '../lib/api';

interface UnitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUnitCreated: () => void;
  propertyId: string;
}

interface CreateUnitData {
  name: string;
  rent_amount: string;
  status: 'vacant' | 'occupied' | 'maintenance';
}

export default function UnitModal({ isOpen, onClose, onUnitCreated, propertyId }: UnitModalProps) {
  const [formData, setFormData] = useState<CreateUnitData>({
    name: '',
    rent_amount: '',
    status: 'vacant',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const rentAmount = parseFloat(formData.rent_amount);
      if (isNaN(rentAmount) || rentAmount < 0) {
        setError('Please enter a valid rent amount');
        setIsSubmitting(false);
        return;
      }

      const response = await propertiesApi.createUnit(propertyId, {
        name: formData.name,
        rent_amount: rentAmount,
        status: formData.status,
      });

      if (response.success) {
        // Reset form
        setFormData({
          name: '',
          rent_amount: '',
          status: 'vacant',
        });
        onUnitCreated();
        onClose();
      } else {
        setError(response.message || 'Failed to create unit');
      }
    } catch (err) {
      setError('An error occurred while creating the unit');
      console.error('Error creating unit:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        name: '',
        rent_amount: '',
        status: 'vacant',
      });
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Add New Unit</h3>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Unit Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Unit Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              placeholder="e.g., Unit 101, Apartment A"
            />
          </div>

          {/* Rent Amount */}
          <div>
            <label htmlFor="rent_amount" className="block text-sm font-medium text-gray-700 mb-1">
              Monthly Rent (KSh) *
            </label>
            <input
              type="number"
              id="rent_amount"
              name="rent_amount"
              value={formData.rent_amount}
              onChange={handleInputChange}
              required
              min="0"
              step="0.01"
              disabled={isSubmitting}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
              placeholder="e.g., 15000"
            />
          </div>

          {/* Status */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 disabled:opacity-50"
            >
              <option value="vacant">Vacant</option>
              <option value="occupied">Occupied</option>
              <option value="maintenance">Under Maintenance</option>
            </select>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.name.trim() || !formData.rent_amount.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Creating...' : 'Create Unit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}