'use client';

import { useState, useEffect, useCallback } from 'react';
import { X, DollarSign, Calendar, CreditCard, FileText } from 'lucide-react';
import {
  Payment,
  CreatePaymentRequest,
  UpdatePaymentRequest,
  PaymentMethod,
  PAYMENT_METHOD_CONFIG
} from '../types/payment';
import { paymentsApi, leasesApi } from '../lib/api';

interface Lease {
  id: string;
  unit_id: string;
  tenant_id: string;
  rent_amount: number;
  start_date: string;
  end_date: string;
  status: string;
  unit_name?: string;
  tenant_name?: string;
  property_title?: string;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  payment?: Payment; // If provided, this is an edit modal
}

export default function PaymentModal({
  isOpen,
  onClose,
  onSuccess,
  payment
}: PaymentModalProps) {
  const [formData, setFormData] = useState<CreatePaymentRequest & UpdatePaymentRequest>({
    lease_id: '',
    amount: 0,
    paid_at: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
    payment_method: undefined,
    notes: '',
  });

  const [leases, setLeases] = useState<Lease[]>([]);
  const [selectedLease, setSelectedLease] = useState<Lease | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const isEditMode = Boolean(payment);

  const fetchLeases = useCallback(async () => {
    try {
      const response = await leasesApi.getLeases();
      if (response.success && response.data) {
        // Only show active leases for new payments
        const leasesData = response.data as Lease[];
        const filteredLeases = isEditMode ? leasesData : leasesData.filter(l => l.status === 'active');
        setLeases(filteredLeases);
      }
    } catch (error) {
      console.error('Error fetching leases:', error);
    }
  }, [isEditMode]);

  useEffect(() => {
    if (isOpen) {
      fetchLeases();
      if (payment) {
        // Pre-fill form for editing
        setFormData({
          lease_id: payment.lease_id,
          amount: payment.amount,
          paid_at: payment.paid_at.split('T')[0], // Convert to YYYY-MM-DD
          payment_method: payment.payment_method,
          notes: payment.notes || '',
        });
      } else {
        // Reset form for new payment
        setFormData({
          lease_id: '',
          amount: 0,
          paid_at: new Date().toISOString().split('T')[0],
          payment_method: undefined,
          notes: '',
        });
        setSelectedLease(null);
      }
      setError(null);
      setValidationErrors({});
    }
  }, [isOpen, payment, fetchLeases]);

  // Update selected lease when lease_id changes
  useEffect(() => {
    if (formData.lease_id && leases.length > 0) {
      const lease = leases.find(l => l.id === formData.lease_id);
      setSelectedLease(lease || null);
    } else {
      setSelectedLease(null);
    }
  }, [formData.lease_id, leases]);

  useEffect(() => {
    if (formData.lease_id) {
      const lease = leases.find(l => l.id === formData.lease_id);
      setSelectedLease(lease || null);
      if (lease && !isEditMode) {
        // Auto-fill amount with lease rent amount for new payments
        setFormData(prev => ({ ...prev, amount: lease.rent_amount }));
      }
    } else {
      setSelectedLease(null);
    }
  }, [formData.lease_id, leases, isEditMode]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate lease selection for new payments
    if (!isEditMode && !formData.lease_id) {
      errors.lease_id = 'Please select a lease';
    }

    // Validate amount
    if (!formData.amount || formData.amount <= 0) {
      errors.amount = 'Amount must be greater than 0';
    }

    // Validate payment date
    if (!formData.paid_at) {
      errors.paid_at = 'Payment date is required';
    } else {
      const paymentDate = new Date(formData.paid_at);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      if (paymentDate > today) {
        errors.paid_at = 'Payment date cannot be in the future';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submitting
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);
    setValidationErrors({});

    try {
      let response;

      if (isEditMode && payment) {
        // Update existing payment
        const updateData: UpdatePaymentRequest = {
          amount: formData.amount,
          paid_at: formData.paid_at,
          payment_method: formData.payment_method,
          notes: formData.notes || undefined,
        };

        response = await paymentsApi.updatePayment(payment.id, updateData);
      } else {
        // Create new payment
        const createData: CreatePaymentRequest = {
          lease_id: formData.lease_id,
          amount: formData.amount,
          paid_at: formData.paid_at,
          payment_method: formData.payment_method,
          notes: formData.notes || undefined,
        };

        response = await paymentsApi.createPayment(createData);
      }

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.message || 'Failed to save payment');
      }
    } catch (error) {
      console.error('Error saving payment:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const paymentMethods: PaymentMethod[] = ['manual', 'mpesa', 'card', 'bank_transfer', 'cash', 'check'];

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {isEditMode ? 'Update Payment' : 'Record New Payment'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Lease Selection (only for new payments) */}
          {!isEditMode && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lease *
              </label>
              <select
                required
                value={formData.lease_id}
                onChange={(e) => setFormData({ ...formData, lease_id: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${validationErrors.lease_id ? 'border-red-300' : ''
                  }`}
              >
                <option value="">Select a lease</option>
                {leases.map((lease) => (
                  <option key={lease.id} value={lease.id}>
                    {lease.tenant_name || 'Unknown Tenant'} - {lease.unit_name || 'Unit'}
                    ({lease.property_title || 'Unknown Property'})
                  </option>
                ))}
              </select>
              {selectedLease && (
                <p className="text-sm text-gray-500 mt-1">
                  Monthly rent: {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(selectedLease.rent_amount)}
                </p>
              )}
              {validationErrors.lease_id && (
                <p className="text-sm text-red-600 mt-1">{validationErrors.lease_id}</p>
              )}
            </div>
          )}

          {/* Payment Info for Edit Mode */}
          {isEditMode && payment && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Information</h4>
              <div className="text-sm text-gray-600">
                <div>Payment ID: #{payment.id.slice(-8)}</div>
                <div>Tenant: {payment.tenant_name || 'Unknown'}</div>
                <div>Unit: {payment.unit_name || 'Unknown'}</div>
              </div>
            </div>
          )}

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="number"
                required
                min="0"
                step="0.01"
                value={formData.amount || ''}
                onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${validationErrors.amount ? 'border-red-300' : ''
                  }`}
                placeholder="0.00"
              />
            </div>
            {validationErrors.amount && (
              <p className="text-sm text-red-600 mt-1">{validationErrors.amount}</p>
            )}
          </div>

          {/* Payment Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Date *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="date"
                required
                value={formData.paid_at}
                onChange={(e) => setFormData({ ...formData, paid_at: e.target.value })}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${validationErrors.paid_at ? 'border-red-300' : ''
                  }`}
              />
            </div>
            {validationErrors.paid_at && (
              <p className="text-sm text-red-600 mt-1">{validationErrors.paid_at}</p>
            )}
          </div>


          {/* Payment Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Method
            </label>
            <div className="relative">
              <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={formData.payment_method || ''}
                onChange={(e) => setFormData({ ...formData, payment_method: e.target.value as PaymentMethod || undefined })}
                className="w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select payment method</option>
                {paymentMethods.map((method) => (
                  <option key={method} value={method}>
                    {PAYMENT_METHOD_CONFIG[method].icon} {PAYMENT_METHOD_CONFIG[method].label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={3}
                className="w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Additional notes about this payment"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !formData.lease_id.trim() || !formData.paid_at.trim() || !formData.payment_method?.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Payment' : 'Record Payment'}
            </button>
          </div>
        </form>
      </div >
    </div >
  );
}