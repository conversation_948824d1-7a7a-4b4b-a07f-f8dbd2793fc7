'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Lease } from '../dashboard/leases/page';

interface LeaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (lease: Lease) => void;
  lease: Lease | null;
}

export default function LeaseModal({ isOpen, onClose, onSave, lease }: LeaseModalProps) {
  const [formData, setFormData] = useState<Partial<Lease>>({
    unit_id: '',
    tenant_id: '',
    start_date: '',
    end_date: '',
    rent_amount: 0,
    deposit_amount: 0,
  });

  useEffect(() => {
    if (lease) {
      setFormData(lease);
    } else {
      setFormData({
        unit_id: '',
        tenant_id: '',
        start_date: '',
        end_date: '',
        rent_amount: 0,
        deposit_amount: 0,
      });
    }
  }, [lease, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData as Lease);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{lease ? 'Edit Lease' : 'Add New Lease'}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label htmlFor="unit_id" className="block text-sm font-medium text-gray-700 mb-1">Unit ID *</label>
            <input type="text" id="unit_id" name="unit_id" value={formData.unit_id} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="tenant_id" className="block text-sm font-medium text-gray-700 mb-1">Tenant ID *</label>
            <input type="text" id="tenant_id" name="tenant_id" value={formData.tenant_id} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">Start Date *</label>
            <input type="date" id="start_date" name="start_date" value={formData.start_date} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input type="date" id="end_date" name="end_date" value={formData.end_date} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="rent_amount" className="block text-sm font-medium text-gray-700 mb-1">Rent Amount *</label>
            <input type="number" id="rent_amount" name="rent_amount" value={formData.rent_amount} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="deposit_amount" className="block text-sm font-medium text-gray-700 mb-1">Deposit Amount</label>
            <input type="number" id="deposit_amount" name="deposit_amount" value={formData.deposit_amount} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
              Cancel
            </button>
            <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
              Save Lease
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
