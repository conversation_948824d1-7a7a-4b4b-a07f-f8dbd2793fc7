'use client';

import { useState, useEffect } from 'react';
import { 
  X, 
  Building, 
  User, 
  DollarSign, 
  Calendar, 
  Clock,
  Paperclip,
  Upload,
  Download,
  FileText,
  Image as ImageIcon
} from 'lucide-react';
import { 
  MaintenanceRequest, 
  Attachment,
  MAINTENANCE_STATUS_CONFIG, 
  MAINTENANCE_PRIORITY_CONFIG 
} from '../types/maintenance';
import { maintenanceApi } from '../lib/api';

interface MaintenanceDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (request: MaintenanceRequest) => void;
  request: MaintenanceRequest | null;
}

export default function MaintenanceDetailModal({ 
  isOpen, 
  onClose, 
  onEdit,
  request 
}: MaintenanceDetailModalProps) {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loadingAttachments, setLoadingAttachments] = useState(false);
  const [uploadingAttachment, setUploadingAttachment] = useState(false);

  useEffect(() => {
    if (isOpen && request) {
      fetchAttachments();
    }
  }, [isOpen, request]);

  const fetchAttachments = async () => {
    if (!request) return;
    
    setLoadingAttachments(true);
    try {
      const response = await maintenanceApi.getAttachments(request.id);
      if (response.success && response.data) {
        setAttachments(response.data as Attachment[]);
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
    } finally {
      setLoadingAttachments(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !request) return;

    setUploadingAttachment(true);
    try {
      // In a real app, you'd upload the file to a storage service first
      // For now, we'll simulate with a placeholder URL
      const attachmentData = {
        url: `https://example.com/uploads/${file.name}`,
        filename: file.name,
        file_size: file.size,
        content_type: file.type,
      };

      const response = await maintenanceApi.createAttachment(request.id, attachmentData);
      if (response.success) {
        fetchAttachments(); // Refresh attachments list
      } else {
        alert('Failed to upload attachment: ' + response.message);
      }
    } catch (error) {
      console.error('Error uploading attachment:', error);
      alert('Failed to upload attachment');
    } finally {
      setUploadingAttachment(false);
      event.target.value = ''; // Reset file input
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getFileIcon = (contentType?: string) => {
    if (!contentType) return <FileText className="h-5 w-5" />;
    if (contentType.startsWith('image/')) return <ImageIcon className="h-5 w-5" />;
    return <FileText className="h-5 w-5" />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  if (!isOpen || !request) return null;

  const statusConfig = MAINTENANCE_STATUS_CONFIG[request.status];
  const priorityConfig = MAINTENANCE_PRIORITY_CONFIG[request.priority];

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Maintenance Request Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Request Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {request.title}
              </h3>
              {request.description && (
                <p className="text-gray-600 mb-4">
                  {request.description}
                </p>
              )}
            </div>
            <button
              onClick={() => onEdit(request)}
              className="ml-4 px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
            >
              Edit
            </button>
          </div>

          {/* Status and Priority */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Status
              </label>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.color}`}>
                {statusConfig.label}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Priority
              </label>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${priorityConfig.color}`}>
                {priorityConfig.label}
              </span>
            </div>
          </div>

          {/* Property and Unit Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Property Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Building className="h-5 w-5 text-gray-400 mr-2" />
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {request.property_title || 'Unknown Property'}
                  </div>
                  <div className="text-sm text-gray-500">
                    Unit: {request.unit_name || 'No specific unit'}
                  </div>
                </div>
              </div>
              {request.tenant_name && (
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {request.tenant_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {request.tenant_email}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Assignment and Cost */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Assigned To
              </label>
              <div className="flex items-center">
                <User className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-900">
                  {request.assigned_to || (
                    <span className="text-gray-500 italic">Unassigned</span>
                  )}
                </span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Cost
              </label>
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-900">
                  {request.cost > 0 ? formatCurrency(request.cost) : (
                    <span className="text-gray-500">No cost recorded</span>
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Timeline</h4>
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-gray-600">Created:</span>
                <span className="ml-2 text-gray-900">{formatDate(request.created_at)}</span>
              </div>
              {request.resolved_at && (
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-gray-600">Resolved:</span>
                  <span className="ml-2 text-gray-900">{formatDate(request.resolved_at)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Attachments */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">Attachments</h4>
              <div className="relative">
                <input
                  type="file"
                  onChange={handleFileUpload}
                  disabled={uploadingAttachment}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                />
                <button
                  disabled={uploadingAttachment}
                  className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Upload className="h-4 w-4 mr-1" />
                  {uploadingAttachment ? 'Uploading...' : 'Upload File'}
                </button>
              </div>
            </div>

            {loadingAttachments ? (
              <div className="text-center py-4">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <p className="text-sm text-gray-500 mt-2">Loading attachments...</p>
              </div>
            ) : attachments.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {attachments.map((attachment) => (
                  <div key={attachment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      {getFileIcon(attachment.content_type)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {attachment.filename || 'Untitled'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatFileSize(attachment.file_size)} • {formatDate(attachment.uploaded_at)}
                        </div>
                      </div>
                    </div>
                    <a
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Download className="h-4 w-4" />
                    </a>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <Paperclip className="mx-auto h-8 w-8 text-gray-400" />
                <p className="text-sm text-gray-500 mt-2">No attachments yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Close
          </button>
          <button
            onClick={() => onEdit(request)}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
          >
            Edit Request
          </button>
        </div>
      </div>
    </div>
  );
}