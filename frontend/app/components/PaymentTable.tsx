'use client';

import { useState } from 'react';
import { 
  Payment, 
  PAYMENT_METHOD_CONFIG,
  PaymentMethod 
} from '../types/payment';
import {
  DollarSign,
  Calendar,
  User,
  Building,
  Eye,
  Edit,
  Trash2,
  CreditCard,
  Plus
} from 'lucide-react';
import { paymentsApi } from '../lib/api';

interface PaymentTableProps {
  payments: Payment[];
  onPaymentUpdated?: () => void;
  onPaymentSelected?: (payment: Payment) => void;
  onPaymentEdit?: (payment: Payment) => void;
  showActions?: boolean;
  onAddNew?: () => void;
}

export default function PaymentTable({
  payments,
  onPaymentUpdated,
  onPaymentSelected,
  onPaymentEdit,
  showActions = true,
  onAddNew
}: PaymentTableProps) {
  const [deletingPayment, setDeletingPayment] = useState<string | null>(null);

  const handleDelete = async (paymentId: string) => {
    if (!confirm('Are you sure you want to delete this payment?')) return;
    
    setDeletingPayment(paymentId);
    try {
      const response = await paymentsApi.deletePayment(paymentId);
      if (response.success) {
        onPaymentUpdated?.();
      } else {
        alert('Failed to delete payment: ' + response.message);
      }
    } catch (error) {
      console.error('Error deleting payment:', error);
      alert('Failed to delete payment');
    } finally {
      setDeletingPayment(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getPaymentMethodBadge = (method?: PaymentMethod) => {
    if (!method) return <span className="text-gray-500 text-sm">Not specified</span>;
    const config = PAYMENT_METHOD_CONFIG[method];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (payments.length === 0) {
    return (
      <div className="text-center py-12">
        <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No payments</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by recording a payment.</p>
        {onAddNew && (
          <div className="mt-6">
            <button
              type="button"
              onClick={onAddNew}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" />
              Record Payment
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Payment Details
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Tenant/Property
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Method
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
            {showActions && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {payments.map((payment) => (
            <tr key={payment.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col">
                  <div className="text-sm font-medium text-gray-900">
                    Payment #{payment.id.slice(-8)}
                  </div>
                  {payment.notes && (
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {payment.notes}
                    </div>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Building className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900">
                      {payment.tenant_name || 'Unknown Tenant'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {payment.unit_name || 'No Unit Specified'}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-green-500 mr-1" />
                  <div className="flex flex-col">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(payment.amount)}
                    </div>
                    {payment.lease_rent_amount && payment.lease_rent_amount !== payment.amount && (
                      <div className="text-xs text-gray-500">
                        of {formatCurrency(payment.lease_rent_amount)} due
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 text-gray-400 mr-2" />
                  {getPaymentMethodBadge(payment.payment_method)}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex flex-col">
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    {formatDate(payment.paid_at)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Recorded {formatDate(payment.created_at)}
                  </div>
                </div>
              </td>
              {showActions && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onPaymentSelected?.(payment)}
                      className="text-indigo-600 hover:text-indigo-900"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onPaymentEdit?.(payment)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Edit Payment"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(payment.id)}
                      disabled={deletingPayment === payment.id}
                      className="text-red-600 hover:text-red-900 disabled:opacity-50"
                      title="Delete Payment"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}