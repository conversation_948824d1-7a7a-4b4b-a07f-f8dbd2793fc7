'use client';

import Link from 'next/link';
import { Building, Users, CreditCard, FileText, BarChart3, Shield, CheckCircle, Star, ArrowRight, Phone, Mail, MessageSquare } from 'lucide-react';

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Simplify Rental</span>{' '}
                  <span className="block text-indigo-600 xl:inline">Management.</span>{' '}
                  <span className="block xl:inline">All in One Place.</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  From tenant applications to rent collection—manage your properties effortlessly with our comprehensive platform built for landlords, property managers, and tenants.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link
                      href="/register"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10 transition-colors"
                    >
                      Get Started Free
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      href="#demo"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 md:py-4 md:text-lg md:px-10 transition-colors"
                    >
                      Request a Demo
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-indigo-400 to-cyan-400 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="bg-white rounded-lg shadow-2xl p-6 max-w-sm mx-4">
              <div className="text-center">
                <div className="bg-green-100 rounded-full p-3 w-16 h-16 mx-auto mb-4">
                  <Building className="w-10 h-10 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard Preview</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Total Properties:</span>
                    <span className="font-semibold">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rent Collected:</span>
                    <span className="font-semibold text-green-600">$24,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Tenants:</span>
                    <span className="font-semibold">28</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Value Propositions Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Why Choose RentBase</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Built for Everyone in Real Estate
            </p>
          </div>
          
          <div className="mt-16">
            <div className="space-y-16 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-x-8">
              {/* For Landlords/Managers */}
              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                  <Building className="h-6 w-6" />
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">For Landlords & Property Managers</p>
                <dd className="mt-2 ml-16 text-base text-gray-500">
                  Track rent payments, automate reminder notifications, manage lease agreements, and get comprehensive financial reports—all from one powerful dashboard.
                </dd>
                <div className="mt-4 ml-16">
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Automated rent collection</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Tenant screening tools</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Financial reporting</li>
                  </ul>
                </div>
              </div>

              {/* For Tenants */}
              <div className="relative">
                <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                  <Users className="h-6 w-6" />
                </div>
                <p className="ml-16 text-lg leading-6 font-medium text-gray-900">For Tenants</p>
                <dd className="mt-2 ml-16 text-base text-gray-500">
                  Pay rent online securely, track maintenance requests in real-time, access important lease documents, and communicate directly with property managers.
                </dd>
                <div className="mt-4 ml-16">
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Online rent payments</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Maintenance request tracking</li>
                    <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Digital lease documents</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Preview Section */}
      <section className="py-16 bg-gray-50" id="demo">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Product Preview</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              See RentBase in Action
            </p>
          </div>

          <div className="mt-16 space-y-12">
            {/* Dashboard Preview */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div>
                <h3 className="text-2xl font-extrabold text-gray-900 sm:text-3xl">
                  Comprehensive Dashboard
                </h3>
                <p className="mt-3 text-lg text-gray-500">
                  Get a complete overview of your properties, rent collection status, maintenance requests, and financial performance all in one place.
                </p>
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <BarChart3 className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Real-time financial metrics</span>
                  </div>
                  <div className="flex items-center">
                    <Building className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Property portfolio overview</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Tenant management tools</span>
                  </div>
                </div>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="bg-white rounded-lg shadow-xl p-6">
                  <div className="border-b border-gray-200 pb-4 mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Property Dashboard</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">$18,500</div>
                      <div className="text-sm text-gray-600">Rent Collected</div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">24</div>
                      <div className="text-sm text-gray-600">Active Leases</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">3</div>
                      <div className="text-sm text-gray-600">Pending Requests</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">96%</div>
                      <div className="text-sm text-gray-600">Occupancy Rate</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Flow Preview */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div className="lg:order-2">
                <h3 className="text-2xl font-extrabold text-gray-900 sm:text-3xl">
                  Seamless Rent Collection
                </h3>
                <p className="mt-3 text-lg text-gray-500">
                  Tenants can pay rent online through multiple payment methods while landlords track payments in real-time with automated notifications.
                </p>
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <CreditCard className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Multiple payment options</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Bank-level security</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Automated receipts</span>
                  </div>
                </div>
              </div>
              <div className="mt-10 lg:mt-0 lg:order-1">
                <div className="bg-white rounded-lg shadow-xl p-6">
                  <div className="text-center">
                    <div className="bg-indigo-100 rounded-full p-3 w-16 h-16 mx-auto mb-4">
                      <CreditCard className="w-10 h-10 text-indigo-600" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Pay Rent</h4>
                    <div className="text-left space-y-3">
                      <div className="bg-gray-50 p-3 rounded">
                        <div className="text-sm text-gray-600">Monthly Rent</div>
                        <div className="text-xl font-bold text-gray-900">$1,250.00</div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded text-sm">Card</button>
                        <button className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded text-sm">Bank</button>
                        <button className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded text-sm">M-Pesa</button>
                      </div>
                      <button className="w-full bg-green-600 text-white py-3 rounded font-semibold">Pay Now</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Maintenance Request Preview */}
            <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
              <div>
                <h3 className="text-2xl font-extrabold text-gray-900 sm:text-3xl">
                  Maintenance Management
                </h3>
                <p className="mt-3 text-lg text-gray-500">
                  Tenants can easily submit maintenance requests with photos and track progress, while property managers can assign contractors and update status in real-time.
                </p>
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <MessageSquare className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Easy request submission</span>
                  </div>
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Photo documentation</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-indigo-500 mr-3" />
                    <span className="text-gray-700">Real-time status updates</span>
                  </div>
                </div>
              </div>
              <div className="mt-10 lg:mt-0">
                <div className="bg-white rounded-lg shadow-xl p-6">
                  <div className="border-b border-gray-200 pb-4 mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Maintenance Requests</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="border border-red-200 bg-red-50 p-3 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">Leaky Faucet</span>
                        <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">Urgent</span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">Unit 2A • Submitted 2 hours ago</div>
                    </div>
                    <div className="border border-yellow-200 bg-yellow-50 p-3 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">AC Repair</span>
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">In Progress</span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">Unit 1B • Assigned to Tech Pro</div>
                    </div>
                    <div className="border border-green-200 bg-green-50 p-3 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">Light Bulb</span>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Completed</span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">Unit 3C • Completed yesterday</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Breakdown Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Complete Feature Set</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Everything You Need to Manage Properties
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              From tenant onboarding to financial reporting, we&apos;ve got you covered with powerful tools designed for modern property management.
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <CreditCard className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Online Rent Collection</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Automate rent collection with multiple payment options including credit cards, bank transfers, and mobile money.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Tenant Screening</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Comprehensive background checks, credit reports, and reference verification to find the best tenants.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <MessageSquare className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Automated Reminders</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Automated email and SMS reminders for rent payments, lease renewals, and important notices.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <FileText className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Maintenance Request Tracking</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Streamlined maintenance workflow with photo uploads, contractor assignment, and progress tracking.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <BarChart3 className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Financial Reports</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Detailed income statements, expense tracking, and tax-ready reports to keep your finances organized.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <div className="flow-root bg-gray-50 rounded-lg px-6 pb-8">
                  <div className="-mt-6">
                    <div>
                      <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                        <Shield className="h-6 w-6 text-white" />
                      </span>
                    </div>
                    <h3 className="mt-8 text-lg font-medium text-gray-900 tracking-tight">Secure Document Storage</h3>
                    <p className="mt-5 text-base text-gray-500">
                      Cloud-based storage for leases, contracts, photos, and important documents with bank-level security.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials / Trust Signals Section */}
      <section className="bg-indigo-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Testimonials</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Trusted by Property Managers Everywhere
            </p>
          </div>

          <div className="mt-16 grid gap-8 lg:grid-cols-3">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;RentBase has completely transformed how I manage my properties. Rent collection is now automatic, and I spend 80% less time on administrative tasks.&quot;
              </p>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-900">Sarah Mitchell</p>
                <p className="text-sm text-gray-600">Property Manager, 15 units</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;As a tenant, I love being able to pay rent online and submit maintenance requests with photos. Everything is so much more convenient now.&quot;
              </p>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-900">Michael Chen</p>
                <p className="text-sm text-gray-600">Tenant</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-4">
                &quot;The financial reporting feature is incredible. Tax season used to be a nightmare, but now everything is organized and ready to go.&quot;
              </p>
              <div className="border-t pt-4">
                <p className="font-semibold text-gray-900">David Rodriguez</p>
                <p className="text-sm text-gray-600">Real Estate Investor, 32 units</p>
              </div>
            </div>
          </div>

          {/* Trust Signals */}
          <div className="mt-16">
            <p className="text-center text-gray-500 text-sm uppercase tracking-wide font-semibold">
              Trusted by industry leaders
            </p>
            <div className="mt-8 grid grid-cols-2 gap-8 md:grid-cols-4">
              <div className="col-span-1 flex justify-center py-8 px-8 bg-white rounded-lg">
                <div className="text-center">
                  <Shield className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Bank-Level Security</p>
                </div>
              </div>
              <div className="col-span-1 flex justify-center py-8 px-8 bg-white rounded-lg">
                <div className="text-center">
                  <CheckCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">GDPR Compliant</p>
                </div>
              </div>
              <div className="col-span-1 flex justify-center py-8 px-8 bg-white rounded-lg">
                <div className="text-center">
                  <Star className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">4.9/5 Rating</p>
                </div>
              </div>
              <div className="col-span-1 flex justify-center py-8 px-8 bg-white rounded-lg">
                <div className="text-center">
                  <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">10,000+ Users</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase">Pricing</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Simple, Transparent Pricing
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Choose the plan that fits your needs. All plans include core features with no hidden fees.
            </p>
          </div>

          <div className="mt-16 space-y-12 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-x-8">
            {/* Pro Tier */}
            <div className="relative p-8 bg-white border border-indigo-200 rounded-2xl shadow-sm flex flex-col ring-2 ring-indigo-500">
              <div className="absolute top-0 right-6 transform -translate-y-1/2">
                <span className="inline-flex rounded-full bg-indigo-500 px-4 py-1 text-sm font-semibold tracking-wider text-white uppercase">
                  Most Popular
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">Professional</h3>
                <p className="mt-4 flex items-baseline text-gray-900">
                  <span className="text-5xl font-extrabold tracking-tight">Kshs 3999</span>
                  <span className="ml-1 text-xl font-semibold">/month</span>
                </p>
                <p className="mt-6 text-gray-500">Advanced automation and reporting for growing property portfolios.</p>
                
                <ul className="mt-6 space-y-6">
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Up to 25 properties</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Automated rent reminders</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Tenant screening tools</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Financial reporting</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Priority support</span>
                  </li>
                </ul>
              </div>
              
              <Link
                href="/register"
                className="mt-8 block w-full py-3 px-6 border border-transparent rounded-md text-center font-medium text-white bg-indigo-500 hover:bg-indigo-600 transition-colors"
              >
                Start 14-day Free Trial
              </Link>
            </div>

            {/* Enterprise Tier */}
            <div className="relative p-8 bg-white border border-gray-200 rounded-2xl shadow-sm flex flex-col">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">Enterprise</h3>
                <p className="mt-4 flex items-baseline text-gray-900">
                  <span className="text-5xl font-extrabold tracking-tight">Kshs 11999</span>
                  <span className="ml-1 text-xl font-semibold">/month</span>
                </p>
                <p className="mt-6 text-gray-500">Complete solution for property management companies and large portfolios.</p>
                
                <ul className="mt-6 space-y-6">
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Unlimited properties</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Multi-user accounts</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Custom integrations</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">Advanced analytics</span>
                  </li>
                  <li className="flex">
                    <CheckCircle className="flex-shrink-0 w-6 h-6 text-green-500" />
                    <span className="ml-3 text-gray-500">24/7 phone support</span>
                  </li>
                </ul>
              </div>
              
              <Link
                href="#contact"
                className="mt-8 block w-full py-3 px-6 border border-transparent rounded-md text-center font-medium text-indigo-600 bg-indigo-50 hover:bg-indigo-100 transition-colors"
              >
                Contact Sales
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="bg-indigo-700">
        <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to simplify your</span>
            <span className="block">rental management?</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-indigo-200">
            Join thousands of landlords and property managers who trust RentBase to streamline their operations.
          </p>
          <Link
            href="/register"
            className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 sm:w-auto transition-colors"
          >
            Start managing today
            <ArrowRight className="ml-2 -mr-1 w-5 h-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800" id="contact">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div className="space-y-8 xl:col-span-1">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-indigo-500" />
                <span className="ml-2 text-xl font-bold text-white">RentBase</span>
              </div>
              <p className="text-gray-300 text-base">
                Simplifying rental management for landlords, property managers, and tenants everywhere.
              </p>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-400 hover:text-gray-300">
                  <span className="sr-only">Twitter</span>
                  <MessageSquare className="h-6 w-6" />
                </a>
                <a href="#" className="text-gray-400 hover:text-gray-300">
                  <span className="sr-only">LinkedIn</span>
                  <Users className="h-6 w-6" />
                </a>
              </div>
            </div>
            <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                    Product
                  </h3>
                  <ul className="mt-4 space-y-4">
                    <li>
                      <a href="#demo" className="text-base text-gray-300 hover:text-white">
                        Features
                      </a>
                    </li>
                    <li>
                      <a href="#demo" className="text-base text-gray-300 hover:text-white">
                        Pricing
                      </a>
                    </li>
                    <li>
                      <a href="/login" className="text-base text-gray-300 hover:text-white">
                        Login
                      </a>
                    </li>
                    <li>
                      <a href="/register" className="text-base text-gray-300 hover:text-white">
                        Sign Up
                      </a>
                    </li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                    Support
                  </h3>
                  <ul className="mt-4 space-y-4">
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Help Center
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Documentation
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Contact Us
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Status
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                    Company
                  </h3>
                  <ul className="mt-4 space-y-4">
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        About
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Blog
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Careers
                      </a>
                    </li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                    Legal
                  </h3>
                  <ul className="mt-4 space-y-4">
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Privacy Policy
                      </a>
                    </li>
                    <li>
                      <a href="#" className="text-base text-gray-300 hover:text-white">
                        Terms of Service
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8">
            <div className="md:flex md:items-center md:justify-between">
              <div className="flex items-center space-x-6">
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-gray-300">+254713588004</span>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
              </div>
              <p className="mt-8 text-base text-gray-400 md:mt-0 md:order-1">
                &copy; 2025 RentBase. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
