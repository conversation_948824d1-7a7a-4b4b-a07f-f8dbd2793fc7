'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { setGlobalAccessToken, profileApi } from '../lib/api';

interface User {
  id: string;
  email: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  accessToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  updateProfile: (name: string) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    // Try to refresh token on app load to check if user is still authenticated
    refreshToken().finally(() => setIsLoading(false));
  }, []);

  const login = async (email: string, password: string) => {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Login failed');
    }

    const data = await response.json();
    console.log('Login successful, setting token:', data.data.access_token);
    setUser(data.data.user);
    setAccessToken(data.data.access_token);
    setGlobalAccessToken(data.data.access_token);
  };

  const register = async (name: string, email: string, password: string) => {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, email, password }),
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Registration failed');
    }

    const data = await response.json();
    setUser(data.data.user);
    setAccessToken(data.data.access_token);
    setGlobalAccessToken(data.data.access_token);
  };

  const logout = async () => {
    try {
      await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      setUser(null);
      setAccessToken(null);
      setGlobalAccessToken(null);
      window.location.href = '/login';
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Token refresh successful, setting token:', data.data.access_token);
        setUser(data.data.user);
        setAccessToken(data.data.access_token);
        setGlobalAccessToken(data.data.access_token);
        return true;
      } else {
        console.log('Token refresh failed, status:', response.status);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    console.log('Clearing tokens after failed refresh');
    setUser(null);
    setAccessToken(null);
    setGlobalAccessToken(null);
    return false;
  };

  const updateProfile = async (name: string): Promise<void> => {
    const response = await profileApi.updateProfile({ name });

    if (!response.success) {
      throw new Error(response.message || 'Failed to update profile');
    }

    // Update local state with new user data and token
    if (response.data && typeof response.data === 'object' && 'user' in response.data && response.data.user) {
      setUser(response.data.user as User);
    }
    if (response.data && typeof response.data === 'object' && 'access_token' in response.data && response.data.access_token) {
      const token = response.data.access_token as string;
      setAccessToken(token);
      setGlobalAccessToken(token);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    const response = await profileApi.changePassword({
      current_password: currentPassword,
      new_password: newPassword,
    });

    if (!response.success) {
      throw new Error(response.message || 'Failed to change password');
    }
  };

  const value: AuthContextType = {
    user,
    accessToken,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
    changePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}