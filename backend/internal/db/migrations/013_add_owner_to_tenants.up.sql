-- Add owner_id column to tenants table for proper data isolation
ALTER TABLE tenants ADD COLUMN owner_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE;

-- Create index for performance on owner_id queries
CREATE INDEX idx_tenants_owner_id ON tenants(owner_id);

-- Update existing tenants to have an owner_id (assign to first user for migration)
-- In production, you would need to properly assign existing tenants to correct owners
UPDATE tenants SET owner_id = (SELECT id FROM users ORDER BY created_at LIMIT 1) WHERE owner_id IS NULL;