-- Drop triggers
DROP TRIGGER IF EXISTS trigger_notifications_updated_at ON notifications;
DROP TRIGGER IF EXISTS trigger_notification_preferences_updated_at ON notification_preferences;

-- Drop function
DROP FUNCTION IF EXISTS update_notification_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_notifications_tenant_id;
DROP INDEX IF EXISTS idx_notifications_lease_id;
DROP INDEX IF EXISTS idx_notifications_maintenance_id;
DROP INDEX IF EXISTS idx_notifications_status;
DROP INDEX IF EXISTS idx_notifications_scheduled_at;
DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notification_preferences_tenant_id;

-- Drop tables
DROP TABLE IF EXISTS notification_preferences;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS notification_templates;