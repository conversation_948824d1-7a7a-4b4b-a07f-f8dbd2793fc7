-- Create notification templates table
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL UNIQUE, -- 'rent_reminder', 'rent_overdue', 'maintenance_resolved'
    message_template TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    lease_id UUID REFERENCES leases(id) ON DELETE CASCADE,
    maintenance_id UUID REFERENCES maintenance_requests(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'cancelled'
    scheduled_at TIMESTAMPTZ NOT NULL,
    sent_at TIMESTAMPTZ,
    error_message TEXT,
    twilio_sid VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notification preferences table
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE UNIQUE,
    rent_reminders_enabled BOOLEAN DEFAULT true,
    reminder_days_before INTEGER DEFAULT 3,
    overdue_notifications_enabled BOOLEAN DEFAULT true,
    maintenance_notifications_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX idx_notifications_lease_id ON notifications(lease_id);
CREATE INDEX idx_notifications_maintenance_id ON notifications(maintenance_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_type ON notifications(type);

-- Create index for notification preferences
CREATE INDEX idx_notification_preferences_tenant_id ON notification_preferences(tenant_id);

-- Insert default notification templates
INSERT INTO notification_templates (type, message_template) VALUES
('rent_reminder', 'Hi {tenant_name}, this is a friendly reminder that your rent payment of ${amount} for {unit_name} is due on {due_date}. Please make your payment to avoid late fees. Thank you!'),
('rent_overdue', 'Dear {tenant_name}, your rent payment of ${amount} for {unit_name} was due on {due_date} and is now overdue. Please contact us immediately to arrange payment. Late fees may apply.'),
('maintenance_resolved', 'Hi {tenant_name}, we''re pleased to inform you that your maintenance request ''{title}'' for {unit_name} has been resolved. Thank you for your patience!');

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notification_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_updated_at();

CREATE TRIGGER trigger_notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_updated_at();