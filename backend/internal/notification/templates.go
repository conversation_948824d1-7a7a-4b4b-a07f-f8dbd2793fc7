package notification

import (
	"fmt"
	"strings"
	"time"

	"github.com/nyunja/rentbase/backend/internal/storage"
)

type NotificationType = storage.NotificationType

const (
	NotificationTypeRentReminder        = storage.NotificationTypeRentReminder
	NotificationTypeRentOverdue         = storage.NotificationTypeRentOverdue
	NotificationTypeMaintenanceResolved = storage.NotificationTypeMaintenanceResolved
	NotificationTypeTenantActivation    = storage.NotificationTypeTenantActivation
)

type MessageTemplate struct {
	Type     NotificationType
	Template string
}

type TemplateData struct {
	TenantName     string
	UnitName       string
	Amount         float64
	DueDate        string
	Title          string
	PropertyName   string
	ActivationLink string
	LandlordName   string
}

var DefaultTemplates = []MessageTemplate{
	{
		Type:     NotificationTypeRentReminder,
		Template: "Hi {tenant_name}, this is a friendly reminder that your rent payment of ${amount} for {unit_name} is due on {due_date}. Please make your payment to avoid late fees. Thank you!",
	},
	{
		Type:     NotificationTypeRentOverdue,
		Template: "Dear {tenant_name}, your rent payment of ${amount} for {unit_name} was due on {due_date} and is now overdue. Please contact us immediately to arrange payment. Late fees may apply.",
	},
	{
		Type:     NotificationTypeMaintenanceResolved,
		Template: "Hi {tenant_name}, we're pleased to inform you that your maintenance request '{title}' for {unit_name} has been resolved. Thank you for your patience!",
	},
	{
		Type:     NotificationTypeTenantActivation,
		Template: "Welcome {tenant_name}! {landlord_name} has created your tenant account. Please activate your account by clicking this link: {activation_link}. This will allow you to access your rental portal and receive important notifications.",
	},
}

func RenderTemplate(template string, data TemplateData) string {
	message := template

	// Replace placeholders with actual values
	message = strings.ReplaceAll(message, "{tenant_name}", data.TenantName)
	message = strings.ReplaceAll(message, "{unit_name}", data.UnitName)
	message = strings.ReplaceAll(message, "{amount}", fmt.Sprintf("%.2f", data.Amount))
	message = strings.ReplaceAll(message, "{due_date}", data.DueDate)
	message = strings.ReplaceAll(message, "{title}", data.Title)
	message = strings.ReplaceAll(message, "{property_name}", data.PropertyName)
	message = strings.ReplaceAll(message, "{activation_link}", data.ActivationLink)
	message = strings.ReplaceAll(message, "{landlord_name}", data.LandlordName)

	return message
}

func FormatCurrency(amount float64) string {
	return fmt.Sprintf("%.2f", amount)
}

func FormatDate(date time.Time) string {
	return date.Format("January 2, 2006")
}

func FormatDateShort(date time.Time) string {
	return date.Format("2006-01-02")
}

func GetDefaultTemplate(notificationType NotificationType) string {
	for _, template := range DefaultTemplates {
		if template.Type == notificationType {
			return template.Template
		}
	}
	return ""
}

func ValidateTemplate(template string) error {
	if template == "" {
		return fmt.Errorf("template cannot be empty")
	}

	// Check for valid placeholders
	validPlaceholders := []string{
		"{tenant_name}",
		"{unit_name}",
		"{amount}",
		"{due_date}",
		"{title}",
		"{property_name}",
		"{activation_link}",
		"{landlord_name}",
	}

	// Simple validation - could be enhanced with more sophisticated checks
	hasPlaceholder := false
	for _, placeholder := range validPlaceholders {
		if strings.Contains(template, placeholder) {
			hasPlaceholder = true
			break
		}
	}

	if !hasPlaceholder {
		return fmt.Errorf("template should contain at least one valid placeholder")
	}

	return nil
}