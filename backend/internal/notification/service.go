package notification

import (
	"context"
	"fmt"
	"time"

	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/storage"
	"go.uber.org/zap"
)

type Service struct {
	store            *storage.NotificationStore
	leaseStore       *storage.LeaseStore
	paymentStore     *storage.PaymentStore
	maintenanceStore *storage.MaintenanceStore
	twilioClient     *TwilioClient
	logger           *zap.Logger
}

func NewService(
	store *storage.NotificationStore,
	leaseStore *storage.LeaseStore,
	paymentStore *storage.PaymentStore,
	maintenanceStore *storage.MaintenanceStore,
	config config.TwilioConfig,
	logger *zap.Logger,
) *Service {
	return &Service{
		store:            store,
		leaseStore:       leaseStore,
		paymentStore:     paymentStore,
		maintenanceStore: maintenanceStore,
		twilioClient:     NewTwilioClient(config, logger),
		logger:           logger,
	}
}

// ProcessPendingNotifications sends all pending notifications
func (s *Service) ProcessPendingNotifications(ctx context.Context) error {
	notifications, err := s.store.GetPendingNotifications(ctx, 50) // Process 50 at a time
	if err != nil {
		s.logger.Error("Failed to get pending notifications", zap.Error(err))
		return err
	}

	s.logger.Info("Processing pending notifications", zap.Int("count", len(notifications)))

	for _, notification := range notifications {
		if err := s.SendNotification(ctx, &notification); err != nil {
			s.logger.Error("Failed to send notification",
				zap.String("notification_id", notification.ID),
				zap.Error(err))
		}
	}

	return nil
}

// SendNotification sends a single notification
func (s *Service) SendNotification(ctx context.Context, notification *storage.Notification) error {
	resp, err := s.twilioClient.SendSMS(ctx, notification.PhoneNumber, notification.Message)
	if err != nil {
		// Update notification with error
		errorMsg := err.Error()
		return s.store.UpdateNotificationStatus(ctx, notification.ID, "failed", nil, &errorMsg)
	}

	// Update notification as sent
	return s.store.UpdateNotificationStatus(ctx, notification.ID, "sent", &resp.SID, nil)
}

// CreateEmailNotification creates and optionally sends an email notification
func (s *Service) CreateEmailNotification(ctx context.Context, email, subject string, notificationType NotificationType, templateData TemplateData, tenantID *string, leaseID *string, maintenanceID *string) error {
	// Get template
	template, err := s.store.GetTemplate(ctx, notificationType)
	if err != nil {
		return fmt.Errorf("failed to get email template: %w", err)
	}
	if template == nil {
		templateStr := GetDefaultTemplate(notificationType)
		if templateStr == "" {
			return fmt.Errorf("no template found for notification type: %s", notificationType)
		}
		template = &storage.NotificationTemplate{
			Type:            notificationType,
			MessageTemplate: templateStr,
		}
	}

	// Render the message
	message := RenderTemplate(template.MessageTemplate, templateData)

	// Send the email directly (since this is for immediate notifications like account activation)
	emailResp, err := s.twilioClient.SendEmail(ctx, email, subject, message)
	if err != nil {
		s.logger.Error("Failed to send email notification",
			zap.String("email", email),
			zap.String("subject", subject),
			zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	s.logger.Info("Email notification sent successfully",
		zap.String("email", email),
		zap.String("subject", subject),
		zap.String("message_id", emailResp.MessageID))

	return nil
}

// CreateRentReminders creates rent reminder notifications for upcoming due dates (background job)
func (s *Service) CreateRentReminders(ctx context.Context) error {
	s.logger.Info("Creating rent reminder notifications")

	// Get all active leases across all owners (safe for background jobs)
	leases, err := s.leaseStore.GetAllActiveLeasesSystemWide(ctx)
	if err != nil {
		return fmt.Errorf("failed to get active leases: %w", err)
	}

	template, err := s.store.GetTemplate(ctx, NotificationTypeRentReminder)
	if err != nil {
		return fmt.Errorf("failed to get rent reminder template: %w", err)
	}
	if template == nil {
		templateStr := GetDefaultTemplate(NotificationTypeRentReminder)
		template = &storage.NotificationTemplate{
			Type:            NotificationTypeRentReminder,
			MessageTemplate: templateStr,
		}
	}

	for _, lease := range leases {
		if lease.TenantID == nil {
			continue
		}

		// Get notification preferences
		prefs, err := s.store.GetNotificationPreferences(ctx, *lease.TenantID)
		if err != nil {
			s.logger.Error("Failed to get notification preferences",
				zap.String("tenant_id", *lease.TenantID),
				zap.Error(err))
			continue
		}

		// Create default preferences if none exist
		if prefs == nil {
			if err := s.store.CreateDefaultPreferences(ctx, *lease.TenantID); err != nil {
				s.logger.Error("Failed to create default preferences",
					zap.String("tenant_id", *lease.TenantID),
					zap.Error(err))
				continue
			}
			// Set default values
			prefs = &storage.NotificationPreference{
				TenantID:                        *lease.TenantID,
				RentRemindersEnabled:            true,
				ReminderDaysBefore:              3,
				OverdueNotificationsEnabled:     true,
				MaintenanceNotificationsEnabled: true,
			}
		}

		if !prefs.RentRemindersEnabled {
			continue
		}

		// Calculate next rent due date (simplified - assumes monthly rent)
		nextDueDate := s.calculateNextRentDueDate(lease.StartDate, time.Now())
		reminderDate := nextDueDate.AddDate(0, 0, -prefs.ReminderDaysBefore)

		// Only create reminder if it should be sent today
		if !s.isSameDay(reminderDate, time.Now()) {
			continue
		}

		// Check if payment already received for this period
		hasPayment, err := s.hasPaymentForPeriod(ctx, lease.ID, nextDueDate)
		if err != nil {
			s.logger.Error("Failed to check payment for period",
				zap.String("lease_id", lease.ID),
				zap.Error(err))
			continue
		}
		if hasPayment {
			continue
		}

		// Get tenant phone from lease (system-wide safe for background jobs)
		tenant, err := s.leaseStore.GetTenantByIDSystemWide(ctx, *lease.TenantID)
		if err != nil || tenant == nil || tenant.Phone == nil || *tenant.Phone == "" {
			s.logger.Warn("Tenant has no phone number",
				zap.String("tenant_id", *lease.TenantID))
			continue
		}

		// Create notification
		unitName := ""
		if lease.UnitName != nil {
			unitName = *lease.UnitName
		}

		templateData := TemplateData{
			TenantName: tenant.Name,
			UnitName:   unitName,
			Amount:     lease.RentAmount,
			DueDate:    FormatDate(nextDueDate),
		}

		message := RenderTemplate(template.MessageTemplate, templateData)

		notification := &storage.Notification{
			TenantID:    lease.TenantID,
			LeaseID:     &lease.ID,
			Type:        NotificationTypeRentReminder,
			PhoneNumber: *tenant.Phone,
			Message:     message,
			Status:      "pending",
			ScheduledAt: time.Now(),
		}

		if err := s.store.CreateNotification(ctx, notification); err != nil {
			s.logger.Error("Failed to create rent reminder notification",
				zap.String("lease_id", lease.ID),
				zap.Error(err))
		} else {
			s.logger.Info("Created rent reminder notification",
				zap.String("lease_id", lease.ID),
				zap.String("tenant_id", *lease.TenantID))
		}
	}

	return nil
}

// CreateOverdueNotifications creates notifications for overdue rent (background job)
func (s *Service) CreateOverdueNotifications(ctx context.Context) error {
	s.logger.Info("Creating overdue rent notifications")

	// Get all active leases across all owners (safe for background jobs)
	leases, err := s.leaseStore.GetAllActiveLeasesSystemWide(ctx)
	if err != nil {
		return fmt.Errorf("failed to get active leases: %w", err)
	}

	template, err := s.store.GetTemplate(ctx, NotificationTypeRentOverdue)
	if err != nil {
		return fmt.Errorf("failed to get rent overdue template: %w", err)
	}
	if template == nil {
		templateStr := GetDefaultTemplate(NotificationTypeRentOverdue)
		template = &storage.NotificationTemplate{
			Type:            NotificationTypeRentOverdue,
			MessageTemplate: templateStr,
		}
	}

	for _, lease := range leases {
		if lease.TenantID == nil {
			continue
		}

		// Get notification preferences
		prefs, err := s.store.GetNotificationPreferences(ctx, *lease.TenantID)
		if err != nil || prefs == nil || !prefs.OverdueNotificationsEnabled {
			continue
		}

		// Calculate rent due dates and check for overdue
		currentTime := time.Now()
		dueDate := s.calculateNextRentDueDate(lease.StartDate, currentTime)

		// Check if rent is overdue (due date has passed)
		if dueDate.After(currentTime) {
			continue
		}

		// Check if payment received for this period
		hasPayment, err := s.hasPaymentForPeriod(ctx, lease.ID, dueDate)
		if err != nil || hasPayment {
			continue
		}

		// Get tenant details (system-wide safe for background jobs)
		tenant, err := s.leaseStore.GetTenantByIDSystemWide(ctx, *lease.TenantID)
		if err != nil || tenant == nil || tenant.Phone == nil || *tenant.Phone == "" {
			continue
		}

		// Create overdue notification
		unitName := ""
		if lease.UnitName != nil {
			unitName = *lease.UnitName
		}

		templateData := TemplateData{
			TenantName: tenant.Name,
			UnitName:   unitName,
			Amount:     lease.RentAmount,
			DueDate:    FormatDate(dueDate),
		}

		message := RenderTemplate(template.MessageTemplate, templateData)

		notification := &storage.Notification{
			TenantID:    lease.TenantID,
			LeaseID:     &lease.ID,
			Type:        NotificationTypeRentOverdue,
			PhoneNumber: *tenant.Phone,
			Message:     message,
			Status:      "pending",
			ScheduledAt: time.Now(),
		}

		if err := s.store.CreateNotification(ctx, notification); err != nil {
			s.logger.Error("Failed to create overdue notification",
				zap.String("lease_id", lease.ID),
				zap.Error(err))
		} else {
			s.logger.Info("Created overdue notification",
				zap.String("lease_id", lease.ID),
				zap.String("tenant_id", *lease.TenantID))
		}
	}

	return nil
}

// CreateMaintenanceResolvedNotification creates notification when maintenance is resolved
func (s *Service) CreateMaintenanceResolvedNotification(ctx context.Context, maintenanceID string, ownerID string) error {
	// Get maintenance request details (owner-protected since this is called from user actions)
	maintenance, err := s.maintenanceStore.GetByID(ctx, maintenanceID, ownerID)
	if err != nil || maintenance == nil {
		return fmt.Errorf("failed to get maintenance request: %w", err)
	}

	// Only send notification if there's a tenant associated
	if maintenance.TenantName == nil || maintenance.TenantEmail == nil {
		return nil
	}

	// Get tenant details to get phone number
	if maintenance.LeaseID == nil {
		return nil
	}

	lease, err := s.leaseStore.GetLeaseByID(ctx, *maintenance.LeaseID)
	if err != nil || lease == nil || lease.TenantID == nil {
		return nil
	}

	tenant, err := s.leaseStore.GetTenantByID(ctx, *lease.TenantID, ownerID)
	if err != nil || tenant == nil || tenant.Phone == nil || *tenant.Phone == "" {
		return nil
	}

	// Check notification preferences
	prefs, err := s.store.GetNotificationPreferences(ctx, *lease.TenantID)
	if err != nil || prefs == nil || !prefs.MaintenanceNotificationsEnabled {
		return nil
	}

	// Get template
	template, err := s.store.GetTemplate(ctx, NotificationTypeMaintenanceResolved)
	if err != nil {
		return fmt.Errorf("failed to get maintenance resolved template: %w", err)
	}
	if template == nil {
		templateStr := GetDefaultTemplate(NotificationTypeMaintenanceResolved)
		template = &storage.NotificationTemplate{
			Type:            NotificationTypeMaintenanceResolved,
			MessageTemplate: templateStr,
		}
	}

	// Create notification
	unitName := ""
	if maintenance.UnitName != nil {
		unitName = *maintenance.UnitName
	}

	templateData := TemplateData{
		TenantName: tenant.Name,
		UnitName:   unitName,
		Title:      maintenance.Title,
	}

	message := RenderTemplate(template.MessageTemplate, templateData)

	notification := &storage.Notification{
		TenantID:      lease.TenantID,
		LeaseID:       maintenance.LeaseID,
		MaintenanceID: &maintenanceID,
		Type:          NotificationTypeMaintenanceResolved,
		PhoneNumber:   *tenant.Phone,
		Message:       message,
		Status:        "pending",
		ScheduledAt:   time.Now(),
	}

	if err := s.store.CreateNotification(ctx, notification); err != nil {
		return fmt.Errorf("failed to create maintenance resolved notification: %w", err)
	}

	s.logger.Info("Created maintenance resolved notification",
		zap.String("maintenance_id", maintenanceID),
		zap.String("tenant_id", *lease.TenantID))

	return nil
}

// Helper functions
func (s *Service) calculateNextRentDueDate(startDate time.Time, currentDate time.Time) time.Time {
	// Simple calculation - assumes monthly rent on the same day each month
	year, month, _ := currentDate.Date()
	day := startDate.Day()

	nextDue := time.Date(year, month, day, 0, 0, 0, 0, currentDate.Location())

	// If the due date for this month has passed, move to next month
	if nextDue.Before(currentDate) {
		nextDue = nextDue.AddDate(0, 1, 0)
	}

	return nextDue
}

func (s *Service) hasPaymentForPeriod(ctx context.Context, leaseID string, dueDate time.Time) (bool, error) {
	// Check for payments in the current period (simplified - checks if payment exists within 30 days of due date)
	startDate := dueDate.AddDate(0, 0, -30)
	endDate := dueDate.AddDate(0, 0, 7) // Give 7 days grace period

	payments, err := s.paymentStore.GetPaymentsByLeaseAndDateRangeSystemWide(ctx, leaseID, startDate, endDate)
	if err != nil {
		return false, err
	}

	return len(payments) > 0, nil
}

func (s *Service) isSameDay(date1, date2 time.Time) bool {
	y1, m1, d1 := date1.Date()
	y2, m2, d2 := date2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}
