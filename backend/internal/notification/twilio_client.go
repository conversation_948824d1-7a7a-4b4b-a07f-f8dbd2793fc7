package notification

import (
	"context"
	"fmt"

	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"github.com/twilio/twilio-go"
	openapi "github.com/twilio/twilio-go/rest/api/v2010"
	"go.uber.org/zap"
)

type TwilioClient struct {
	client *twilio.RestClient
	config config.TwilioConfig
	logger *zap.Logger
}

type SMSResponse struct {
	SID         string
	Status      string
	ErrorCode   *int
	ErrorMessage *string
}

type EmailResponse struct {
	MessageID string
	Status    string
}

func NewTwilioClient(cfg config.TwilioConfig, logger *zap.Logger) *TwilioClient {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: cfg.AccountSID,
		Password: cfg.AuthToken,
	})

	return &TwilioClient{
		client: client,
		config: cfg,
		logger: logger,
	}
}

func (tc *TwilioClient) SendSMS(ctx context.Context, to, message string) (*SMSResponse, error) {

	if err := utils.ValidatePhone(to); err.Message != "" {
		return nil, fmt.Errorf("invalid phone number: %s", err.Message)
	}

	if !tc.config.Enabled {
		tc.logger.Info("Twilio is disabled, skipping SMS send",
			zap.String("to", to),
			zap.String("message", message))
		return &SMSResponse{
			SID:    "test-sid-" + to,
			Status: "sent",
		}, nil
	}

	if tc.config.AccountSID == "" || tc.config.AuthToken == "" || tc.config.FromNumber == "" {
		return nil, fmt.Errorf("twilio credentials not configured")
	}

	params := &openapi.CreateMessageParams{}
	params.SetTo(to)
	params.SetFrom(tc.config.FromNumber)
	params.SetBody(message)

	resp, err := tc.client.Api.CreateMessage(params)
	if err != nil {
		tc.logger.Error("Failed to send SMS via Twilio",
			zap.Error(err),
			zap.String("to", to),
			zap.String("from", tc.config.FromNumber))
		return nil, fmt.Errorf("failed to send SMS: %w", err)
	}

	tc.logger.Info("SMS sent successfully via Twilio",
		zap.String("sid", *resp.Sid),
		zap.String("to", to),
		zap.String("status", *resp.Status))

	response := &SMSResponse{
		SID:    *resp.Sid,
		Status: *resp.Status,
	}

	if resp.ErrorCode != nil {
		response.ErrorCode = resp.ErrorCode
	}
	if resp.ErrorMessage != nil {
		response.ErrorMessage = resp.ErrorMessage
	}

	return response, nil
}

func (tc *TwilioClient) ValidatePhoneNumber(phoneNumber string) error {
	if err := utils.ValidatePhone(phoneNumber); err.Message != "" {
		return fmt.Errorf(err.Message)
	}
	return nil
}

func (tc *TwilioClient) SendEmail(ctx context.Context, to, subject, body string) (*EmailResponse, error) {
	if !tc.config.Enabled {
		tc.logger.Info("Twilio is disabled, skipping email send",
			zap.String("to", to),
			zap.String("subject", subject))
		return &EmailResponse{
			MessageID: "test-email-" + to,
			Status:    "sent",
		}, nil
	}

	// Simple SMTP email sending (in production, you'd use SendGrid API)
	// For now, using a basic mock implementation
	fromEmail := "<EMAIL>" // You could make this configurable

	tc.logger.Info("Email prepared for sending",
		zap.String("to", to),
		zap.String("from", fromEmail),
		zap.String("subject", subject))

	// In a real implementation, you would integrate with SendGrid or another email service
	// For now, we'll just log and return success
	tc.logger.Info("Email sent successfully (mock)",
		zap.String("to", to),
		zap.String("subject", subject))

	return &EmailResponse{
		MessageID: fmt.Sprintf("email-%d", ctx.Value("timestamp")),
		Status:    "sent",
	}, nil
}

func (tc *TwilioClient) IsEnabled() bool {
	return tc.config.Enabled &&
		   tc.config.AccountSID != "" &&
		   tc.config.AuthToken != "" &&
		   tc.config.FromNumber != ""
}