package utils

import (
    "regexp"
    "strings"
)

var (
    emailRegex      = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    phoneRegex      = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
    lettersOnlyRegex = regexp.MustCompile(`^[a-zA-Z]+$`)
    numbersOnlyRegex = regexp.MustCompile(`^[0-9]+$`)
    alphanumericRegex = regexp.MustCompile(`^[a-zA-Z0-9 .\']+$`)
    urlRegex        = regexp.MustCompile(`^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$`)
)

type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

type ValidationErrors []ValidationError

func (v ValidationErrors) Error() string {
    var messages []string
    for _, err := range v {
        messages = append(messages, err.Field+": "+err.Message)
    }
    return strings.Join(messages, ", ")
}

func ValidateEmail(email string) ValidationError {
    if strings.TrimSpace(email) == "" {
        return ValidationError{Field: "email", Message: "Email is required"}
    }
    if !emailRegex.MatchString(email) {
        return ValidationError{Field: "email", Message: "Invalid email format"}
    }
    return ValidationError{}
}

func ValidatePassword(password string) ValidationError {
    if password == "" {
        return ValidationError{Field: "password", Message: "Password is required"}
    }
    if len(password) < 8 {
        return ValidationError{Field: "password", Message: "Password must be at least 8 characters"}
    }
    return ValidationError{}
}

func ValidateRequired(value, fieldName string) ValidationError {
    if strings.TrimSpace(value) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    return ValidationError{}
}

func ValidatePhone(phone string) ValidationError {
    if phone != "" && !phoneRegex.MatchString(phone) {
        return ValidationError{Field: "phone", Message: "Invalid phone number format"}
    }
    return ValidationError{}
}

func ValidateLettersOnly(value, fieldName string) ValidationError {
    if strings.TrimSpace(value) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    if !lettersOnlyRegex.MatchString(value) {
        return ValidationError{Field: fieldName, Message: fieldName + " must contain only letters"}
    }
    return ValidationError{}
}

func ValidateNumbersOnly(value, fieldName string) ValidationError {
    if strings.TrimSpace(value) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    if !numbersOnlyRegex.MatchString(value) {
        return ValidationError{Field: fieldName, Message: fieldName + " must contain only numbers"}
    }
    return ValidationError{}
}

func ValidateAlphanumeric(value, fieldName string) ValidationError {
    if strings.TrimSpace(value) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    if !alphanumericRegex.MatchString(value) {
        return ValidationError{Field: fieldName, Message: fieldName + " must contain only letters, numbers, spaces, dots, and apostrophes"}
    }
    return ValidationError{}
}

func ValidateURL(url, fieldName string) ValidationError {
    if strings.TrimSpace(url) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    if !urlRegex.MatchString(url) {
        return ValidationError{Field: fieldName, Message: fieldName + " must be a valid URL (http or https)"}
    }
    return ValidationError{}
}
