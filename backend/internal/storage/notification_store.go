package storage

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
)

type NotificationType string

const (
	NotificationTypeRentReminder        NotificationType = "rent_reminder"
	NotificationTypeRentOverdue         NotificationType = "rent_overdue"
	NotificationTypeMaintenanceResolved NotificationType = "maintenance_resolved"
	NotificationTypeTenantActivation    NotificationType = "tenant_activation"
)

type NotificationTemplate struct {
	ID              string           `json:"id" db:"id"`
	Type            NotificationType `json:"type" db:"type"`
	MessageTemplate string           `json:"message_template" db:"message_template"`
	CreatedAt       time.Time        `json:"created_at" db:"created_at"`
}

type Notification struct {
	ID            string           `json:"id" db:"id"`
	TenantID      *string          `json:"tenant_id" db:"tenant_id"`
	LeaseID       *string          `json:"lease_id" db:"lease_id"`
	MaintenanceID *string          `json:"maintenance_id" db:"maintenance_id"`
	Type          NotificationType `json:"type" db:"type"`
	PhoneNumber   string           `json:"phone_number" db:"phone_number"`
	Message       string           `json:"message" db:"message"`
	Status        string           `json:"status" db:"status"`
	ScheduledAt   time.Time        `json:"scheduled_at" db:"scheduled_at"`
	SentAt        *time.Time       `json:"sent_at" db:"sent_at"`
	ErrorMessage  *string          `json:"error_message" db:"error_message"`
	TwilioSID     *string          `json:"twilio_sid" db:"twilio_sid"`
	CreatedAt     time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at" db:"updated_at"`
}

type NotificationPreference struct {
	ID                             string    `json:"id" db:"id"`
	TenantID                       string    `json:"tenant_id" db:"tenant_id"`
	RentRemindersEnabled           bool      `json:"rent_reminders_enabled" db:"rent_reminders_enabled"`
	ReminderDaysBefore             int       `json:"reminder_days_before" db:"reminder_days_before"`
	OverdueNotificationsEnabled    bool      `json:"overdue_notifications_enabled" db:"overdue_notifications_enabled"`
	MaintenanceNotificationsEnabled bool     `json:"maintenance_notifications_enabled" db:"maintenance_notifications_enabled"`
	CreatedAt                      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt                      time.Time `json:"updated_at" db:"updated_at"`
}

type NotificationStore struct {
	db *pgxpool.Pool
}

func NewNotificationStore(db *pgxpool.Pool) *NotificationStore {
	return &NotificationStore{db: db}
}

// Template operations
func (ns *NotificationStore) GetTemplate(ctx context.Context, notificationType NotificationType) (*NotificationTemplate, error) {
	query := `SELECT id, type, message_template, created_at FROM notification_templates WHERE type = $1`

	var template NotificationTemplate
	err := ns.db.QueryRow(ctx, query, notificationType).Scan(
		&template.ID,
		&template.Type,
		&template.MessageTemplate,
		&template.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &template, nil
}

func (ns *NotificationStore) UpdateTemplate(ctx context.Context, templateID, messageTemplate string) error {
	query := `UPDATE notification_templates SET message_template = $1 WHERE id = $2`
	_, err := ns.db.Exec(ctx, query, messageTemplate, templateID)
	return err
}

// Notification operations
func (ns *NotificationStore) CreateNotification(ctx context.Context, notification *Notification) error {
	if notification.ID == "" {
		notification.ID = uuid.New().String()
	}

	query := `
		INSERT INTO notifications (id, tenant_id, lease_id, maintenance_id, type, phone_number, message, status, scheduled_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
	`

	_, err := ns.db.Exec(ctx, query,
		notification.ID,
		notification.TenantID,
		notification.LeaseID,
		notification.MaintenanceID,
		notification.Type,
		notification.PhoneNumber,
		notification.Message,
		notification.Status,
		notification.ScheduledAt,
	)

	return err
}

func (ns *NotificationStore) GetPendingNotifications(ctx context.Context, limit int) ([]Notification, error) {
	query := `
		SELECT id, tenant_id, lease_id, maintenance_id, type, phone_number, message, status,
		       scheduled_at, sent_at, error_message, twilio_sid, created_at, updated_at
		FROM notifications
		WHERE status = 'pending' AND scheduled_at <= NOW()
		ORDER BY scheduled_at ASC
		LIMIT $1
	`

	rows, err := ns.db.Query(ctx, query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var notifications []Notification
	for rows.Next() {
		var n Notification
		err := rows.Scan(
			&n.ID,
			&n.TenantID,
			&n.LeaseID,
			&n.MaintenanceID,
			&n.Type,
			&n.PhoneNumber,
			&n.Message,
			&n.Status,
			&n.ScheduledAt,
			&n.SentAt,
			&n.ErrorMessage,
			&n.TwilioSID,
			&n.CreatedAt,
			&n.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, n)
	}

	return notifications, rows.Err()
}

func (ns *NotificationStore) UpdateNotificationStatus(ctx context.Context, notificationID, status string, twilioSID *string, errorMessage *string) error {
	var query string
	var args []interface{}

	if status == "sent" {
		query = `UPDATE notifications SET status = $1, twilio_sid = $2, sent_at = NOW(), updated_at = NOW() WHERE id = $3`
		args = []interface{}{status, twilioSID, notificationID}
	} else {
		query = `UPDATE notifications SET status = $1, error_message = $2, updated_at = NOW() WHERE id = $3`
		args = []interface{}{status, errorMessage, notificationID}
	}

	_, err := ns.db.Exec(ctx, query, args...)
	return err
}

func (ns *NotificationStore) GetNotificationHistory(ctx context.Context, tenantID string, limit, offset int) ([]Notification, error) {
	query := `
		SELECT id, tenant_id, lease_id, maintenance_id, type, phone_number, message, status,
		       scheduled_at, sent_at, error_message, twilio_sid, created_at, updated_at
		FROM notifications
		WHERE tenant_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := ns.db.Query(ctx, query, tenantID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var notifications []Notification
	for rows.Next() {
		var n Notification
		err := rows.Scan(
			&n.ID,
			&n.TenantID,
			&n.LeaseID,
			&n.MaintenanceID,
			&n.Type,
			&n.PhoneNumber,
			&n.Message,
			&n.Status,
			&n.ScheduledAt,
			&n.SentAt,
			&n.ErrorMessage,
			&n.TwilioSID,
			&n.CreatedAt,
			&n.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, n)
	}

	return notifications, rows.Err()
}

// Notification preferences operations
func (ns *NotificationStore) GetNotificationPreferences(ctx context.Context, tenantID string) (*NotificationPreference, error) {
	query := `
		SELECT id, tenant_id, rent_reminders_enabled, reminder_days_before,
		       overdue_notifications_enabled, maintenance_notifications_enabled, created_at, updated_at
		FROM notification_preferences
		WHERE tenant_id = $1
	`

	var pref NotificationPreference
	err := ns.db.QueryRow(ctx, query, tenantID).Scan(
		&pref.ID,
		&pref.TenantID,
		&pref.RentRemindersEnabled,
		&pref.ReminderDaysBefore,
		&pref.OverdueNotificationsEnabled,
		&pref.MaintenanceNotificationsEnabled,
		&pref.CreatedAt,
		&pref.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &pref, nil
}

func (ns *NotificationStore) CreateDefaultPreferences(ctx context.Context, tenantID string) error {
	id := uuid.New().String()
	query := `
		INSERT INTO notification_preferences (id, tenant_id, rent_reminders_enabled, reminder_days_before,
		                                    overdue_notifications_enabled, maintenance_notifications_enabled, created_at, updated_at)
		VALUES ($1, $2, true, 3, true, true, NOW(), NOW())
		ON CONFLICT (tenant_id) DO NOTHING
	`

	_, err := ns.db.Exec(ctx, query, id, tenantID)
	return err
}

func (ns *NotificationStore) UpdateNotificationPreferences(ctx context.Context, pref *NotificationPreference) error {
	query := `
		UPDATE notification_preferences
		SET rent_reminders_enabled = $1, reminder_days_before = $2,
		    overdue_notifications_enabled = $3, maintenance_notifications_enabled = $4, updated_at = NOW()
		WHERE tenant_id = $5
	`

	_, err := ns.db.Exec(ctx, query,
		pref.RentRemindersEnabled,
		pref.ReminderDaysBefore,
		pref.OverdueNotificationsEnabled,
		pref.MaintenanceNotificationsEnabled,
		pref.TenantID,
	)

	return err
}