package storage

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type User struct {
	ID           string    `json:"id" db:"id"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"`
	Name         *string   `json:"name" db:"name"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

type UserStore struct {
	db *pgxpool.Pool
}

func NewUserStore(db *pgxpool.Pool) *UserStore {
	return &UserStore{db: db}
}

func (r *UserStore) Create(ctx context.Context, email, passwordHash, name string) (*User, error) {
	query := `
        INSERT INTO users (email, password_hash, name)
        VALUES ($1, $2, $3)
        RETURNING id, email, name, created_at, updated_at`

	var user User
	err := r.db.QueryRow(ctx, query, email, passwordHash, name).Scan(
		&user.ID, &user.Email, &user.Name, &user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserStore) GetByEmail(ctx context.Context, email string) (*User, error) {
	query := `
        SELECT id, email, password_hash, name, created_at, updated_at
        FROM users
        WHERE email = $1`

	var user User
	err := r.db.QueryRow(ctx, query, email).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.Name,
		&user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserStore) GetByID(ctx context.Context, userID string) (*User, error) {
	query := `
        SELECT id, email, name, created_at, updated_at
        FROM users
        WHERE id = $1`

	var user User
	err := r.db.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.Name, &user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserStore) GetByIDWithPassword(ctx context.Context, userID string) (*User, error) {
	query := `
        SELECT id, email, password_hash, name, created_at, updated_at
        FROM users
        WHERE id = $1`

	var user User
	err := r.db.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Email, &user.PasswordHash, &user.Name,
		&user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserStore) Update(ctx context.Context, userID, name string) (*User, error) {
	query := `
        UPDATE users
        SET name = $2, updated_at = now()
        WHERE id = $1
        RETURNING id, email, name, created_at, updated_at`

	var user User
	err := r.db.QueryRow(ctx, query, userID, name).Scan(
		&user.ID, &user.Email, &user.Name, &user.CreatedAt, &user.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserStore) Delete(ctx context.Context, userID string) error {
	query := `DELETE FROM users WHERE id = $1`
	_, err := r.db.Exec(ctx, query, userID)
	return err
}

func (r *UserStore) UpdatePassword(ctx context.Context, userID, passwordHash string) error {
	query := `
        UPDATE users
        SET password_hash = $2, updated_at = now()
        WHERE id = $1`

	_, err := r.db.Exec(ctx, query, userID, passwordHash)
	return err
}

func (r *UserStore) EmailExists(ctx context.Context, email string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)`
	var exists bool
	err := r.db.QueryRow(ctx, query, email).Scan(&exists)
	return exists, err
}
