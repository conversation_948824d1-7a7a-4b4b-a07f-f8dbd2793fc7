package storage

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type MaintenanceRequest struct {
	ID          string     `json:"id" db:"id"`
	UnitID      *string    `json:"unit_id" db:"unit_id"`
	LeaseID     *string    `json:"lease_id" db:"lease_id"`
	Title       string     `json:"title" db:"title"`
	Description *string    `json:"description" db:"description"`
	Status      string     `json:"status" db:"status"`
	Priority    string     `json:"priority" db:"priority"`
	CreatedBy   *string    `json:"created_by" db:"created_by"`
	AssignedTo  *string    `json:"assigned_to" db:"assigned_to"`
	Cost        float64    `json:"cost" db:"cost"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	ResolvedAt  *time.Time `json:"resolved_at" db:"resolved_at"`

	// Joined fields
	UnitName       *string `json:"unit_name,omitempty" db:"unit_name"`
	PropertyID     *string `json:"property_id,omitempty" db:"property_id"`
	PropertyTitle  *string `json:"property_title,omitempty" db:"property_title"`
	TenantName     *string `json:"tenant_name,omitempty" db:"tenant_name"`
	TenantEmail    *string `json:"tenant_email,omitempty" db:"tenant_email"`
	LeaseActive    *bool   `json:"lease_active,omitempty" db:"lease_active"`
}

type Attachment struct {
	ID          string    `json:"id" db:"id"`
	ParentType  string    `json:"parent_type" db:"parent_type"`
	ParentID    string    `json:"parent_id" db:"parent_id"`
	URL         string    `json:"url" db:"url"`
	Filename    *string   `json:"filename" db:"filename"`
	FileSize    *int64    `json:"file_size" db:"file_size"`
	ContentType *string   `json:"content_type" db:"content_type"`
	UploadedBy  *string   `json:"uploaded_by" db:"uploaded_by"`
	UploadedAt  time.Time `json:"uploaded_at" db:"uploaded_at"`
}

type MaintenanceStore struct {
	db *pgxpool.Pool
}

func NewMaintenanceStore(db *pgxpool.Pool) *MaintenanceStore {
	return &MaintenanceStore{db: db}
}

func (s *MaintenanceStore) Create(ctx context.Context, unitID, leaseID *string, title string, description *string, priority string, createdBy *string) (*MaintenanceRequest, error) {
	if unitID == nil && leaseID == nil {
		return nil, errors.New("either unit_id or lease_id must be provided")
	}

	query := `
		INSERT INTO maintenance_requests (unit_id, lease_id, title, description, priority, created_by)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, unit_id, lease_id, title, description, status, priority, created_by, assigned_to, cost, created_at, resolved_at`

	var req MaintenanceRequest
	err := s.db.QueryRow(ctx, query, unitID, leaseID, title, description, priority, createdBy).Scan(
		&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
		&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt)
	if err != nil {
		return nil, err
	}

	return &req, nil
}

func (s *MaintenanceStore) GetByOwner(ctx context.Context, ownerID string) ([]MaintenanceRequest, error) {
	query := `
		SELECT 
			m.id, m.unit_id, m.lease_id, m.title, m.description, m.status, m.priority,
			m.created_by, m.assigned_to, m.cost, m.created_at, m.resolved_at,
			u.name as unit_name, p.id as property_id, p.title as property_title,
			t.name as tenant_name, t.email as tenant_email, l.active as lease_active
		FROM maintenance_requests m
		LEFT JOIN units u ON m.unit_id = u.id
		LEFT JOIN properties p ON u.property_id = p.id
		LEFT JOIN leases l ON m.lease_id = l.id
		LEFT JOIN tenants t ON l.tenant_id = t.id
		WHERE 
			(m.unit_id IS NOT NULL AND p.owner_id = $1) OR
			(m.lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l2 
				JOIN units u2 ON l2.unit_id = u2.id 
				JOIN properties p2 ON u2.property_id = p2.id 
				WHERE l2.id = m.lease_id AND p2.owner_id = $1
			))
		ORDER BY 
			CASE m.priority 
				WHEN 'urgent' THEN 1 
				WHEN 'high' THEN 2 
				WHEN 'medium' THEN 3 
				ELSE 4 
			END,
			m.created_at DESC`

	rows, err := s.db.Query(ctx, query, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var requests []MaintenanceRequest
	for rows.Next() {
		var req MaintenanceRequest
		err := rows.Scan(
			&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
			&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt,
			&req.UnitName, &req.PropertyID, &req.PropertyTitle, &req.TenantName, &req.TenantEmail, &req.LeaseActive)
		if err != nil {
			return nil, err
		}
		requests = append(requests, req)
	}

	return requests, nil
}

func (s *MaintenanceStore) GetByID(ctx context.Context, requestID, ownerID string) (*MaintenanceRequest, error) {
	query := `
		SELECT 
			m.id, m.unit_id, m.lease_id, m.title, m.description, m.status, m.priority,
			m.created_by, m.assigned_to, m.cost, m.created_at, m.resolved_at,
			u.name as unit_name, p.id as property_id, p.title as property_title,
			t.name as tenant_name, t.email as tenant_email, l.active as lease_active
		FROM maintenance_requests m
		LEFT JOIN units u ON m.unit_id = u.id
		LEFT JOIN properties p ON u.property_id = p.id
		LEFT JOIN leases l ON m.lease_id = l.id
		LEFT JOIN tenants t ON l.tenant_id = t.id
		WHERE m.id = $1 AND (
			(m.unit_id IS NOT NULL AND p.owner_id = $2) OR
			(m.lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l2 
				JOIN units u2 ON l2.unit_id = u2.id 
				JOIN properties p2 ON u2.property_id = p2.id 
				WHERE l2.id = m.lease_id AND p2.owner_id = $2
			))
		)`

	var req MaintenanceRequest
	err := s.db.QueryRow(ctx, query, requestID, ownerID).Scan(
		&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
		&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt,
		&req.UnitName, &req.PropertyID, &req.PropertyTitle, &req.TenantName, &req.TenantEmail, &req.LeaseActive)
	if err != nil {
		return nil, err
	}

	return &req, nil
}

func (s *MaintenanceStore) Update(ctx context.Context, requestID, ownerID string, status *string, assignedTo *string, cost *float64, description *string) (*MaintenanceRequest, error) {
	// Build dynamic update query
	setParts := []string{}
	args := []any{requestID, ownerID}
	argIndex := 3

	if status != nil {
		setParts = append(setParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *status)
		argIndex++
		
		// If status is resolved, set resolved_at timestamp
		if *status == "resolved" {
			setParts = append(setParts, "resolved_at = now()")
		}
	}

	if assignedTo != nil {
		setParts = append(setParts, fmt.Sprintf("assigned_to = $%d", argIndex))
		args = append(args, *assignedTo)
		argIndex++
	}

	if cost != nil {
		setParts = append(setParts, fmt.Sprintf("cost = $%d", argIndex))
		args = append(args, *cost)
		argIndex++
	}

	if description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *description)
		argIndex++
	}

	if len(setParts) == 0 {
		return nil, errors.New("no fields to update")
	}

	query := `
		UPDATE maintenance_requests 
		SET ` + setParts[0]
	for i := 1; i < len(setParts); i++ {
		query += ", " + setParts[i]
	}
	query += `
		WHERE id = $1 AND (
			(unit_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM units u JOIN properties p ON u.property_id = p.id 
				WHERE u.id = maintenance_requests.unit_id AND p.owner_id = $2
			)) OR
			(lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l 
				JOIN units u ON l.unit_id = u.id 
				JOIN properties p ON u.property_id = p.id 
				WHERE l.id = maintenance_requests.lease_id AND p.owner_id = $2
			))
		)
		RETURNING id, unit_id, lease_id, title, description, status, priority, created_by, assigned_to, cost, created_at, resolved_at`

	var req MaintenanceRequest
	err := s.db.QueryRow(ctx, query, args...).Scan(
		&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
		&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt)
	if err != nil {
		return nil, err
	}

	return &req, nil
}

func (s *MaintenanceStore) Delete(ctx context.Context, requestID, ownerID string) error {
	query := `
		DELETE FROM maintenance_requests 
		WHERE id = $1 AND (
			(unit_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM units u JOIN properties p ON u.property_id = p.id 
				WHERE u.id = maintenance_requests.unit_id AND p.owner_id = $2
			)) OR
			(lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l 
				JOIN units u ON l.unit_id = u.id 
				JOIN properties p ON u.property_id = p.id 
				WHERE l.id = maintenance_requests.lease_id AND p.owner_id = $2
			))
		)`

	result, err := s.db.Exec(ctx, query, requestID, ownerID)
	if err != nil {
		return err
	}

	if result.RowsAffected() == 0 {
		return errors.New("maintenance request not found or unauthorized")
	}

	return nil
}

func (s *MaintenanceStore) GetByStatus(ctx context.Context, ownerID, status string) ([]MaintenanceRequest, error) {
	query := `
		SELECT 
			m.id, m.unit_id, m.lease_id, m.title, m.description, m.status, m.priority,
			m.created_by, m.assigned_to, m.cost, m.created_at, m.resolved_at,
			u.name as unit_name, p.id as property_id, p.title as property_title,
			t.name as tenant_name, t.email as tenant_email, l.active as lease_active
		FROM maintenance_requests m
		LEFT JOIN units u ON m.unit_id = u.id
		LEFT JOIN properties p ON u.property_id = p.id
		LEFT JOIN leases l ON m.lease_id = l.id
		LEFT JOIN tenants t ON l.tenant_id = t.id
		WHERE m.status = $2 AND (
			(m.unit_id IS NOT NULL AND p.owner_id = $1) OR
			(m.lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l2 
				JOIN units u2 ON l2.unit_id = u2.id 
				JOIN properties p2 ON u2.property_id = p2.id 
				WHERE l2.id = m.lease_id AND p2.owner_id = $1
			))
		)
		ORDER BY 
			CASE m.priority 
				WHEN 'urgent' THEN 1 
				WHEN 'high' THEN 2 
				WHEN 'medium' THEN 3 
				ELSE 4 
			END,
			m.created_at DESC`

	rows, err := s.db.Query(ctx, query, ownerID, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var requests []MaintenanceRequest
	for rows.Next() {
		var req MaintenanceRequest
		err := rows.Scan(
			&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
			&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt,
			&req.UnitName, &req.PropertyID, &req.PropertyTitle, &req.TenantName, &req.TenantEmail, &req.LeaseActive)
		if err != nil {
			return nil, err
		}
		requests = append(requests, req)
	}

	return requests, nil
}

func (s *MaintenanceStore) GetByUnitID(ctx context.Context, unitID, ownerID string) ([]MaintenanceRequest, error) {
	query := `
		SELECT 
			m.id, m.unit_id, m.lease_id, m.title, m.description, m.status, m.priority,
			m.created_by, m.assigned_to, m.cost, m.created_at, m.resolved_at,
			u.name as unit_name, p.id as property_id, p.title as property_title,
			t.name as tenant_name, t.email as tenant_email, l.active as lease_active
		FROM maintenance_requests m
		JOIN units u ON m.unit_id = u.id
		JOIN properties p ON u.property_id = p.id
		LEFT JOIN leases l ON m.lease_id = l.id
		LEFT JOIN tenants t ON l.tenant_id = t.id
		WHERE m.unit_id = $1 AND p.owner_id = $2
		ORDER BY m.created_at DESC`

	rows, err := s.db.Query(ctx, query, unitID, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var requests []MaintenanceRequest
	for rows.Next() {
		var req MaintenanceRequest
		err := rows.Scan(
			&req.ID, &req.UnitID, &req.LeaseID, &req.Title, &req.Description, &req.Status,
			&req.Priority, &req.CreatedBy, &req.AssignedTo, &req.Cost, &req.CreatedAt, &req.ResolvedAt,
			&req.UnitName, &req.PropertyID, &req.PropertyTitle, &req.TenantName, &req.TenantEmail, &req.LeaseActive)
		if err != nil {
			return nil, err
		}
		requests = append(requests, req)
	}

	return requests, nil
}

type MaintenanceSummary struct {
	PendingCount    int     `json:"pending_count" db:"pending_count"`
	InProgressCount int     `json:"in_progress_count" db:"in_progress_count"`
	ResolvedCount   int     `json:"resolved_count" db:"resolved_count"`
	UrgentCount     int     `json:"urgent_count" db:"urgent_count"`
	HighCount       int     `json:"high_count" db:"high_count"`
	TotalCost       float64 `json:"total_cost" db:"total_cost"`
	AvgResolutionDays *float64 `json:"avg_resolution_days" db:"avg_resolution_days"`
}

func (s *MaintenanceStore) GetSummary(ctx context.Context, ownerID string) (*MaintenanceSummary, error) {
	query := `
		SELECT 
			COUNT(CASE WHEN m.status = 'pending' THEN 1 END) as pending_count,
			COUNT(CASE WHEN m.status = 'in_progress' THEN 1 END) as in_progress_count,
			COUNT(CASE WHEN m.status = 'resolved' THEN 1 END) as resolved_count,
			COUNT(CASE WHEN m.priority = 'urgent' THEN 1 END) as urgent_count,
			COUNT(CASE WHEN m.priority = 'high' THEN 1 END) as high_count,
			COALESCE(SUM(m.cost), 0) as total_cost,
			AVG(CASE WHEN m.resolved_at IS NOT NULL 
				THEN EXTRACT(EPOCH FROM (m.resolved_at - m.created_at))/86400 
				END) as avg_resolution_days
		FROM maintenance_requests m
		LEFT JOIN units u ON m.unit_id = u.id
		LEFT JOIN properties p ON u.property_id = p.id
		WHERE 
			(m.unit_id IS NOT NULL AND p.owner_id = $1) OR
			(m.lease_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM leases l2 
				JOIN units u2 ON l2.unit_id = u2.id 
				JOIN properties p2 ON u2.property_id = p2.id 
				WHERE l2.id = m.lease_id AND p2.owner_id = $1
			))`

	var summary MaintenanceSummary
	err := s.db.QueryRow(ctx, query, ownerID).Scan(
		&summary.PendingCount, &summary.InProgressCount, &summary.ResolvedCount,
		&summary.UrgentCount, &summary.HighCount, &summary.TotalCost, &summary.AvgResolutionDays)
	if err != nil {
		return nil, err
	}

	return &summary, nil
}

// Attachment methods
func (s *MaintenanceStore) CreateAttachment(ctx context.Context, parentType, parentID, url string, filename *string, fileSize *int64, contentType *string, uploadedBy *string) (*Attachment, error) {
	query := `
		INSERT INTO attachments (parent_type, parent_id, url, filename, file_size, content_type, uploaded_by)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, parent_type, parent_id, url, filename, file_size, content_type, uploaded_by, uploaded_at`

	var attachment Attachment
	err := s.db.QueryRow(ctx, query, parentType, parentID, url, filename, fileSize, contentType, uploadedBy).Scan(
		&attachment.ID, &attachment.ParentType, &attachment.ParentID, &attachment.URL,
		&attachment.Filename, &attachment.FileSize, &attachment.ContentType, &attachment.UploadedBy, &attachment.UploadedAt)
	if err != nil {
		return nil, err
	}

	return &attachment, nil
}

func (s *MaintenanceStore) GetAttachments(ctx context.Context, parentType, parentID string) ([]Attachment, error) {
	query := `
		SELECT id, parent_type, parent_id, url, filename, file_size, content_type, uploaded_by, uploaded_at
		FROM attachments
		WHERE parent_type = $1 AND parent_id = $2
		ORDER BY uploaded_at DESC`

	rows, err := s.db.Query(ctx, query, parentType, parentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var attachments []Attachment
	for rows.Next() {
		var attachment Attachment
		err := rows.Scan(
			&attachment.ID, &attachment.ParentType, &attachment.ParentID, &attachment.URL,
			&attachment.Filename, &attachment.FileSize, &attachment.ContentType, &attachment.UploadedBy, &attachment.UploadedAt)
		if err != nil {
			return nil, err
		}
		attachments = append(attachments, attachment)
	}

	return attachments, nil
}