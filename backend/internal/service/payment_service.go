package service

import (
	"context"
	"errors"
	"time"

	"github.com/nyunja/rentbase/backend/internal/storage"
)

type PaymentService struct {
	paymentStore *storage.PaymentStore
	leaseStore   *storage.LeaseStore
}

func NewPaymentService(paymentStore *storage.PaymentStore, leaseStore *storage.LeaseStore) *PaymentService {
	return &PaymentService{
		paymentStore: paymentStore,
		leaseStore:   leaseStore,
	}
}

func (s *PaymentService) RecordPayment(ctx context.Context, leaseID string, amount float64, paidAt time.Time, paymentMethod, notes, createdBy *string, userID string) (*storage.Payment, error) {
	if amount <= 0 {
		return nil, errors.New("payment amount must be positive")
	}

	lease, err := s.leaseStore.GetLeaseByIDWithOwnership(ctx, leaseID, userID)
	if err != nil {
		return nil, errors.New("lease not found or access denied")
	}

	if !lease.Active {
		return nil, errors.New("cannot record payment for inactive lease")
	}

	return s.paymentStore.Create(ctx, leaseID, amount, paidAt, paymentMethod, notes, createdBy)
}

func (s *PaymentService) GetPaymentsByOwner(ctx context.Context, ownerID string) ([]storage.Payment, error) {
	return s.paymentStore.GetByOwner(ctx, ownerID)
}

func (s *PaymentService) GetPaymentsByLease(ctx context.Context, leaseID string, ownerID string) ([]storage.Payment, error) {
	_, err := s.leaseStore.GetLeaseByIDWithOwnership(ctx, leaseID, ownerID)
	if err != nil {
		return nil, errors.New("lease not found or access denied")
	}

	return s.paymentStore.GetByLeaseID(ctx, leaseID)
}

func (s *PaymentService) GetPaymentByID(ctx context.Context, paymentID, ownerID string) (*storage.Payment, error) {
	return s.paymentStore.GetByID(ctx, paymentID, ownerID)
}

func (s *PaymentService) UpdatePayment(ctx context.Context, paymentID, ownerID string, amount float64, paidAt time.Time, paymentMethod, notes *string) (*storage.Payment, error) {
	if amount <= 0 {
		return nil, errors.New("payment amount must be positive")
	}

	return s.paymentStore.Update(ctx, paymentID, ownerID, amount, paidAt, paymentMethod, notes)
}

func (s *PaymentService) DeletePayment(ctx context.Context, paymentID, ownerID string) error {
	return s.paymentStore.Delete(ctx, paymentID, ownerID)
}

func (s *PaymentService) GetRecentPayments(ctx context.Context, ownerID string, limit int) ([]storage.Payment, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	return s.paymentStore.GetRecent(ctx, ownerID, limit)
}

func (s *PaymentService) GetPaymentSummary(ctx context.Context, ownerID string, startDate, endDate time.Time) (*storage.PaymentSummary, error) {
	if startDate.After(endDate) {
		return nil, errors.New("start date cannot be after end date")
	}

	return s.paymentStore.GetSummaryByPeriod(ctx, ownerID, startDate, endDate)
}

func (s *PaymentService) ValidatePaymentMethod(method string) bool {
	validMethods := map[string]bool{
		"manual":        true,
		"mpesa":         true,
		"card":          true,
		"bank_transfer": true,
		"cash":          true,
		"check":         true,
	}
	return validMethods[method]
}

func (s *PaymentService) GetMonthlyPayments(ctx context.Context, ownerID string, year int, month int) ([]storage.Payment, error) {
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	payments, err := s.paymentStore.GetByOwner(ctx, ownerID)
	if err != nil {
		return nil, err
	}

	var monthlyPayments []storage.Payment
	for _, payment := range payments {
		if payment.PaidAt.After(startDate) && payment.PaidAt.Before(endDate) {
			monthlyPayments = append(monthlyPayments, payment)
		}
	}

	return monthlyPayments, nil
}