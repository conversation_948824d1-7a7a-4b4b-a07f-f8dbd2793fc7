package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/nyunja/rentbase/backend/internal/notification"
	"github.com/nyunja/rentbase/backend/internal/storage"
)

type LeaseService struct {
	leaseStore          *storage.LeaseStore
	notificationService *notification.Service
}

func NewLeaseService(leaseStore *storage.LeaseStore, notificationService *notification.Service) *LeaseService {
	return &LeaseService{
		leaseStore:          leaseStore,
		notificationService: notificationService,
	}
}

func (s *LeaseService) CreateTenant(ctx context.Context, name string, email, phone *string, ownerID string) (*storage.Tenant, error) {
	if name == "" {
		return nil, errors.New("tenant name is required")
	}
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}
	if email == nil || *email == "" {
		return nil, errors.New("tenant email is required for account activation")
	}

	// Create the tenant
	tenant, err := s.leaseStore.CreateTenant(ctx, name, email, phone, ownerID)
	if err != nil {
		return nil, err
	}

	// Send activation email if notification service is available
	if s.notificationService != nil {
		go s.sendActivationEmail(context.Background(), tenant, ownerID)
	}

	return tenant, nil
}

func (s *LeaseService) sendActivationEmail(ctx context.Context, tenant *storage.Tenant, ownerID string) {
	// Generate activation token/link (in a real app, this would be a secure token)
	activationToken := uuid.New().String()
	activationLink := fmt.Sprintf("https://yourapp.com/activate?token=%s&tenant_id=%s", activationToken, tenant.ID)

	// Get landlord info (for now, using ownerID as landlord name)
	landlordName := ownerID // In a real app, you'd fetch the actual landlord name

	// Prepare template data
	templateData := notification.TemplateData{
		TenantName:     tenant.Name,
		ActivationLink: activationLink,
		LandlordName:   landlordName,
	}

	// Send the email notification
	err := s.notificationService.CreateEmailNotification(
		ctx,
		*tenant.Email,
		"Account Activation - Welcome to RentBase",
		notification.NotificationTypeTenantActivation,
		templateData,
		&tenant.ID,
		nil, // no lease ID for activation
		nil, // no maintenance ID
	)

	if err != nil {
		// Log error but don't fail tenant creation
		fmt.Printf("Failed to send activation email to tenant %s: %v\n", tenant.ID, err)
	}
}

func (s *LeaseService) GetTenantsByOwner(ctx context.Context, ownerID string) ([]storage.Tenant, error) {
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}
	return s.leaseStore.GetTenantsByOwner(ctx, ownerID)
}

func (s *LeaseService) GetTenantByID(ctx context.Context, tenantID string, ownerID string) (*storage.Tenant, error) {
	if tenantID == "" {
		return nil, errors.New("tenant ID is required")
	}
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}

	tenant, err := s.leaseStore.GetTenantByID(ctx, tenantID, ownerID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("tenant not found")
		}
		return nil, err
	}

	return tenant, nil
}

func (s *LeaseService) CreateLease(ctx context.Context, unitID string, tenantID *string, startDate time.Time, endDate *time.Time, rentAmount, depositAmount float64) (*storage.Lease, error) {
	if unitID == "" {
		return nil, errors.New("unit ID is required")
	}

	if rentAmount < 0 {
		return nil, errors.New("rent amount cannot be negative")
	}

	if depositAmount < 0 {
		return nil, errors.New("deposit amount cannot be negative")
	}

	if endDate != nil && endDate.Before(startDate) {
		return nil, errors.New("end date cannot be before start date")
	}

	return s.leaseStore.CreateLease(ctx, unitID, tenantID, startDate, endDate, rentAmount, depositAmount)
}

func (s *LeaseService) GetLeasesByOwner(ctx context.Context, ownerID string, activeOnly bool) ([]storage.Lease, error) {
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}

	return s.leaseStore.GetLeasesByOwner(ctx, ownerID, activeOnly)
}

func (s *LeaseService) GetLeaseByID(ctx context.Context, leaseID string) (*storage.Lease, error) {
	if leaseID == "" {
		return nil, errors.New("lease ID is required")
	}

	lease, err := s.leaseStore.GetLeaseByID(ctx, leaseID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("lease not found")
		}
		return nil, err
	}

	return lease, nil
}

func (s *LeaseService) GetLeaseByIDWithOwnership(ctx context.Context, leaseID string, ownerID string) (*storage.Lease, error) {
	if leaseID == "" {
		return nil, errors.New("lease ID is required")
	}
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}

	lease, err := s.leaseStore.GetLeaseByIDWithOwnership(ctx, leaseID, ownerID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("lease not found")
		}
		return nil, err
	}

	return lease, nil
}

func (s *LeaseService) DeactivateLease(ctx context.Context, leaseID string) error {
	if leaseID == "" {
		return errors.New("lease ID is required")
	}

	return s.leaseStore.DeactivateLease(ctx, leaseID)
}

func (s *LeaseService) GetActiveLeasesByOwner(ctx context.Context, ownerID string) ([]storage.Lease, error) {
	return s.GetLeasesByOwner(ctx, ownerID, true)
}

func (s *LeaseService) GetAllLeasesByOwner(ctx context.Context, ownerID string) ([]storage.Lease, error) {
	return s.GetLeasesByOwner(ctx, ownerID, false)
}

func (s *LeaseService) VerifyUnitOwnership(ctx context.Context, unitID string, ownerID string) error {
	if unitID == "" {
		return errors.New("unit ID is required")
	}
	if ownerID == "" {
		return errors.New("owner ID is required")
	}

	err := s.leaseStore.GetUnitByIDWithOwnership(ctx, unitID, ownerID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.New("unit not found or access denied")
		}
		return err
	}

	return nil
}