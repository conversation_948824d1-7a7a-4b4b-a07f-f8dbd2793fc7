package service

import (
	"context"
	"errors"

	"github.com/nyunja/rentbase/backend/internal/storage"
)

type MaintenanceService struct {
	maintenanceStore *storage.MaintenanceStore
	propertyStore    *storage.PropertyStore
	leaseStore       *storage.LeaseStore
}

func NewMaintenanceService(maintenanceStore *storage.MaintenanceStore, propertyStore *storage.PropertyStore, leaseStore *storage.LeaseStore) *MaintenanceService {
	return &MaintenanceService{
		maintenanceStore: maintenanceStore,
		propertyStore:    propertyStore,
		leaseStore:       leaseStore,
	}
}

func (s *MaintenanceService) CreateRequest(ctx context.Context, unitID, leaseID *string, title string, description *string, priority string, createdBy, userID string) (*storage.MaintenanceRequest, error) {
	if title == "" {
		return nil, errors.New("title is required")
	}

	if unitID == nil && leaseID == nil {
		return nil, errors.New("either unit_id or lease_id must be provided")
	}

	if !s.isValidPriority(priority) {
		return nil, errors.New("invalid priority level")
	}

	// Verify ownership
	if unitID != nil {
		units, err := s.propertyStore.GetUnitsByOwner(ctx, userID)
		if err != nil {
			return nil, errors.New("failed to verify unit ownership")
		}
		
		unitExists := false
		for _, unit := range units {
			if unit.ID == *unitID {
				unitExists = true
				break
			}
		}
		if !unitExists {
			return nil, errors.New("unit not found or unauthorized")
		}
	}

	if leaseID != nil {
		leases, err := s.leaseStore.GetLeasesByOwner(ctx, userID, false)
		if err != nil {
			return nil, errors.New("failed to verify lease ownership")
		}
		
		leaseExists := false
		for _, lease := range leases {
			if lease.ID == *leaseID {
				leaseExists = true
				break
			}
		}
		if !leaseExists {
			return nil, errors.New("lease not found or unauthorized")
		}
	}

	createdByPtr := &createdBy
	return s.maintenanceStore.Create(ctx, unitID, leaseID, title, description, priority, createdByPtr)
}

func (s *MaintenanceService) GetRequestsByOwner(ctx context.Context, ownerID string) ([]storage.MaintenanceRequest, error) {
	return s.maintenanceStore.GetByOwner(ctx, ownerID)
}

func (s *MaintenanceService) GetRequestByID(ctx context.Context, requestID, ownerID string) (*storage.MaintenanceRequest, error) {
	return s.maintenanceStore.GetByID(ctx, requestID, ownerID)
}

func (s *MaintenanceService) UpdateRequest(ctx context.Context, requestID, ownerID string, status *string, assignedTo *string, cost *float64, description *string) (*storage.MaintenanceRequest, error) {
	if status != nil && !s.isValidStatus(*status) {
		return nil, errors.New("invalid status")
	}

	if cost != nil && *cost < 0 {
		return nil, errors.New("cost cannot be negative")
	}

	return s.maintenanceStore.Update(ctx, requestID, ownerID, status, assignedTo, cost, description)
}

func (s *MaintenanceService) DeleteRequest(ctx context.Context, requestID, ownerID string) error {
	return s.maintenanceStore.Delete(ctx, requestID, ownerID)
}

func (s *MaintenanceService) GetRequestsByStatus(ctx context.Context, ownerID, status string) ([]storage.MaintenanceRequest, error) {
	if !s.isValidStatus(status) {
		return nil, errors.New("invalid status")
	}

	return s.maintenanceStore.GetByStatus(ctx, ownerID, status)
}

func (s *MaintenanceService) GetPendingRequests(ctx context.Context, ownerID string) ([]storage.MaintenanceRequest, error) {
	return s.maintenanceStore.GetByStatus(ctx, ownerID, "pending")
}

func (s *MaintenanceService) GetActiveRequests(ctx context.Context, ownerID string) ([]storage.MaintenanceRequest, error) {
	return s.maintenanceStore.GetByStatus(ctx, ownerID, "in_progress")
}

func (s *MaintenanceService) GetRequestsByUnit(ctx context.Context, unitID, ownerID string) ([]storage.MaintenanceRequest, error) {
	// Verify unit ownership
	units, err := s.propertyStore.GetUnitsByOwner(ctx, ownerID)
	if err != nil {
		return nil, errors.New("failed to verify unit ownership")
	}
	
	unitExists := false
	for _, unit := range units {
		if unit.ID == unitID {
			unitExists = true
			break
		}
	}
	if !unitExists {
		return nil, errors.New("unit not found or unauthorized")
	}

	return s.maintenanceStore.GetByUnitID(ctx, unitID, ownerID)
}

func (s *MaintenanceService) GetMaintenanceSummary(ctx context.Context, ownerID string) (*storage.MaintenanceSummary, error) {
	return s.maintenanceStore.GetSummary(ctx, ownerID)
}

func (s *MaintenanceService) CreateAttachment(ctx context.Context, requestID, ownerID string, url string, filename *string, fileSize *int64, contentType *string, uploadedBy string) (*storage.Attachment, error) {
	// Verify maintenance request ownership
	_, err := s.maintenanceStore.GetByID(ctx, requestID, ownerID)
	if err != nil {
		return nil, errors.New("maintenance request not found or unauthorized")
	}

	uploadedByPtr := &uploadedBy
	return s.maintenanceStore.CreateAttachment(ctx, "maintenance", requestID, url, filename, fileSize, contentType, uploadedByPtr)
}

func (s *MaintenanceService) GetAttachments(ctx context.Context, requestID, ownerID string) ([]storage.Attachment, error) {
	// Verify maintenance request ownership
	_, err := s.maintenanceStore.GetByID(ctx, requestID, ownerID)
	if err != nil {
		return nil, errors.New("maintenance request not found or unauthorized")
	}

	return s.maintenanceStore.GetAttachments(ctx, "maintenance", requestID)
}

func (s *MaintenanceService) isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"pending":     true,
		"in_progress": true,
		"resolved":    true,
		"cancelled":   true,
	}
	return validStatuses[status]
}

func (s *MaintenanceService) isValidPriority(priority string) bool {
	validPriorities := map[string]bool{
		"low":    true,
		"medium": true,
		"high":   true,
		"urgent": true,
	}
	return validPriorities[priority]
}

func (s *MaintenanceService) ValidateStatusTransition(currentStatus, newStatus string) error {
	if currentStatus == newStatus {
		return nil // No change
	}

	// Define allowed transitions
	transitions := map[string][]string{
		"pending":     {"in_progress", "cancelled"},
		"in_progress": {"resolved", "cancelled"},
		"resolved":    {"in_progress"}, // Can reopen if issue persists
		"cancelled":   {"pending"},     // Can restart if needed
	}

	allowedNext, exists := transitions[currentStatus]
	if !exists {
		return errors.New("invalid current status")
	}

	for _, allowed := range allowedNext {
		if allowed == newStatus {
			return nil
		}
	}

	return errors.New("invalid status transition from " + currentStatus + " to " + newStatus)
}