package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

type LeaseHandler struct {
	leaseService *service.LeaseService
	logger       *zap.Logger
}

func NewLeaseHandler(leaseService *service.LeaseService, logger *zap.Logger) *LeaseHandler {
	return &LeaseHandler{
		leaseService: leaseService,
		logger:       logger,
	}
}

func (h *LeaseHandler) CreateTenant(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	var req dto.CreateTenantRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	tenant, err := h.leaseService.CreateTenant(r.Context(), req.Name, req.Email, req.Phone, userID)
	if err != nil {
		h.logger.Error("Failed to create tenant", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create tenant")
		return
	}

	response := dto.TenantToResponse(tenant)
	utils.WriteSuccessResponse(w, response, "Tenant created successfully")
}

func (h *LeaseHandler) GetTenants(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	tenants, err := h.leaseService.GetTenantsByOwner(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get tenants", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve tenants")
		return
	}

	response := dto.TenantsToResponse(tenants)
	utils.WriteSuccessResponse(w, response, "Tenants retrieved successfully")
}

func (h *LeaseHandler) GetTenant(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	tenantID := chi.URLParam(r, "id")
	if tenantID == "" {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Tenant ID is required")
		return
	}

	tenant, err := h.leaseService.GetTenantByID(r.Context(), tenantID, userID)
	if err != nil {
		if err.Error() == "tenant not found" {
			utils.WriteErrorResponse(w, http.StatusNotFound, "Tenant not found")
			return
		}
		h.logger.Error("Failed to get tenant", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve tenant")
		return
	}

	response := dto.TenantToResponse(tenant)
	utils.WriteSuccessResponse(w, response, "Tenant retrieved successfully")
}

func (h *LeaseHandler) CreateLease(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	var req dto.CreateLeaseRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid start date format")
		return
	}

	var endDate *time.Time
	if req.EndDate != nil && *req.EndDate != "" {
		parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate)
		if err != nil {
			utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid end date format")
			return
		}
		endDate = &parsedEndDate
	}

	// Verify user owns the unit before creating lease
	if err := h.leaseService.VerifyUnitOwnership(r.Context(), req.UnitID, userID); err != nil {
		if err.Error() == "unit not found or access denied" {
			utils.WriteErrorResponse(w, http.StatusForbidden, "Unit not found or access denied")
			return
		}
		h.logger.Error("Failed to verify unit ownership", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to verify unit ownership")
		return
	}

	lease, err := h.leaseService.CreateLease(r.Context(), req.UnitID, req.TenantID, startDate, endDate, req.RentAmount, req.DepositAmount)
	if err != nil {
		h.logger.Error("Failed to create lease", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create lease")
		return
	}

	response := dto.LeaseToResponse(lease)
	utils.WriteSuccessResponse(w, response, "Lease created successfully")
}

func (h *LeaseHandler) GetLeases(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	activeOnly := false
	if activeParam := r.URL.Query().Get("active"); activeParam != "" {
		if active, err := strconv.ParseBool(activeParam); err == nil {
			activeOnly = active
		}
	}

	leases, err := h.leaseService.GetLeasesByOwner(r.Context(), userID, activeOnly)
	if err != nil {
		h.logger.Error("Failed to get leases", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve leases")
		return
	}

	response := dto.LeasesToResponse(leases)
	utils.WriteSuccessResponse(w, response, "Leases retrieved successfully")
}

func (h *LeaseHandler) GetLease(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	leaseID := chi.URLParam(r, "id")
	if leaseID == "" {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Lease ID is required")
		return
	}

	lease, err := h.leaseService.GetLeaseByIDWithOwnership(r.Context(), leaseID, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			utils.WriteErrorResponse(w, http.StatusNotFound, "Lease not found")
			return
		}
		h.logger.Error("Failed to get lease", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve lease")
		return
	}

	response := dto.LeaseToResponse(lease)
	utils.WriteSuccessResponse(w, response, "Lease retrieved successfully")
}

func (h *LeaseHandler) DeactivateLease(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	leaseID := chi.URLParam(r, "id")
	if leaseID == "" {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Lease ID is required")
		return
	}

	// Verify lease exists and user has access
	_, err := h.leaseService.GetLeaseByIDWithOwnership(r.Context(), leaseID, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			utils.WriteErrorResponse(w, http.StatusNotFound, "Lease not found")
			return
		}
		h.logger.Error("Failed to get lease for deactivation", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve lease")
		return
	}

	if err := h.leaseService.DeactivateLease(r.Context(), leaseID); err != nil {
		h.logger.Error("Failed to deactivate lease", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to deactivate lease")
		return
	}

	// Return updated lease
	updatedLease, err := h.leaseService.GetLeaseByID(r.Context(), leaseID)
	if err != nil {
		h.logger.Error("Failed to get updated lease", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve updated lease")
		return
	}

	response := dto.LeaseToResponse(updatedLease)
	utils.WriteSuccessResponse(w, response, "Lease deactivated successfully")
}