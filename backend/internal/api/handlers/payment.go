package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

type PaymentHandler struct {
	paymentService *service.PaymentService
	logger         *zap.Logger
}

func NewPaymentHandler(paymentService *service.PaymentService, logger *zap.Logger) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		logger:         logger,
	}
}

func (h *PaymentHandler) CreatePayment(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	var req dto.CreatePaymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode payment request", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if validationErrors := req.Validate(); len(validationErrors) > 0 {
		h.logger.Warn("Payment validation failed", zap.Any("errors", validationErrors))
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return
	}

	paidAt, err := req.GetPaidAtTime()
	if err != nil {
		h.logger.Error("Failed to parse payment date", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid payment date")
		return
	}

	createdBy := &userID
	payment, err := h.paymentService.RecordPayment(r.Context(), req.LeaseID, req.Amount, paidAt, req.PaymentMethod, req.Notes, createdBy, userID)
	if err != nil {
		h.logger.Error("Failed to record payment", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to record payment: "+err.Error())
		return
	}

	response := dto.PaymentToResponse(payment)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) GetPayments(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	leaseID := r.URL.Query().Get("lease_id")
	if leaseID != "" {
		payments, err := h.paymentService.GetPaymentsByLease(r.Context(), leaseID, userID)
		if err != nil {
			if err.Error() == "lease not found or access denied" {
				utils.WriteErrorResponse(w, http.StatusForbidden, "Lease not found or access denied")
				return
			}
			h.logger.Error("Failed to get payments by lease", zap.Error(err))
			utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get payments")
			return
		}

		response := dto.PaymentsToResponse(payments)
		utils.WriteSuccessResponse(w, response)
		return
	}

	payments, err := h.paymentService.GetPaymentsByOwner(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get payments", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get payments")
		return
	}

	response := dto.PaymentsToResponse(payments)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) GetPayment(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	paymentID := chi.URLParam(r, "id")

	payment, err := h.paymentService.GetPaymentByID(r.Context(), paymentID, userID)
	if err != nil {
		h.logger.Error("Failed to get payment", zap.Error(err), zap.String("payment_id", paymentID))
		utils.WriteErrorResponse(w, http.StatusNotFound, "Payment not found")
		return
	}

	response := dto.PaymentToResponse(payment)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) UpdatePayment(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	paymentID := chi.URLParam(r, "id")

	var req dto.UpdatePaymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode payment update request", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if validationErrors := req.Validate(); len(validationErrors) > 0 {
		h.logger.Warn("Payment update validation failed", zap.Any("errors", validationErrors))
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return
	}

	paidAt, err := req.GetPaidAtTime()
	if err != nil {
		h.logger.Error("Failed to parse payment date", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid payment date")
		return
	}

	payment, err := h.paymentService.UpdatePayment(r.Context(), paymentID, userID, req.Amount, paidAt, req.PaymentMethod, req.Notes)
	if err != nil {
		h.logger.Error("Failed to update payment", zap.Error(err), zap.String("payment_id", paymentID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to update payment: "+err.Error())
		return
	}

	response := dto.PaymentToResponse(payment)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) DeletePayment(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	paymentID := chi.URLParam(r, "id")

	err := h.paymentService.DeletePayment(r.Context(), paymentID, userID)
	if err != nil {
		h.logger.Error("Failed to delete payment", zap.Error(err), zap.String("payment_id", paymentID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to delete payment: "+err.Error())
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (h *PaymentHandler) GetRecentPayments(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	limitStr := r.URL.Query().Get("limit")
	limit := 10
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	payments, err := h.paymentService.GetRecentPayments(r.Context(), userID, limit)
	if err != nil {
		h.logger.Error("Failed to get recent payments", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get recent payments")
		return
	}

	response := dto.PaymentsToResponse(payments)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) GetPaymentSummary(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	startDateStr := r.URL.Query().Get("start_date")
	endDateStr := r.URL.Query().Get("end_date")

	var startDate, endDate time.Time
	var err error

	if startDateStr == "" {
		startDate = time.Now().AddDate(0, -1, 0)
	} else {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
	}

	if endDateStr == "" {
		endDate = time.Now()
	} else {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
	}

	summary, err := h.paymentService.GetPaymentSummary(r.Context(), userID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to get payment summary", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get payment summary")
		return
	}

	response := dto.PaymentSummaryToResponse(summary)
	utils.WriteSuccessResponse(w, response)
}

func (h *PaymentHandler) GetMonthlyPayments(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	yearStr := chi.URLParam(r, "year")
	monthStr := chi.URLParam(r, "month")

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2000 || year > 3000 {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid year")
		return
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid month")
		return
	}

	payments, err := h.paymentService.GetMonthlyPayments(r.Context(), userID, year, month)
	if err != nil {
		h.logger.Error("Failed to get monthly payments", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get monthly payments")
		return
	}

	response := dto.PaymentsToResponse(payments)
	utils.WriteSuccessResponse(w, response)
}