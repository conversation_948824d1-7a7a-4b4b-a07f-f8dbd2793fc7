package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

type MaintenanceHandler struct {
	maintenanceService *service.MaintenanceService
	logger             *zap.Logger
}

func NewMaintenanceHandler(maintenanceService *service.MaintenanceService, logger *zap.Logger) *MaintenanceHandler {
	return &MaintenanceHandler{
		maintenanceService: maintenanceService,
		logger:             logger,
	}
}

func (h *MaintenanceHandler) CreateRequest(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	var req dto.CreateMaintenanceRequestRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode maintenance request", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if validationErrors := req.Validate(); len(validationErrors) > 0 {
		h.logger.Warn("Maintenance request validation failed", zap.Any("errors", validationErrors))
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return
	}

	maintenanceReq, err := h.maintenanceService.CreateRequest(r.Context(), req.UnitID, req.LeaseID, req.Title, req.Description, req.Priority, userID, userID)
	if err != nil {
		h.logger.Error("Failed to create maintenance request", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create maintenance request: "+err.Error())
		return
	}

	response := dto.MaintenanceRequestToResponse(maintenanceReq)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetRequests(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	status := r.URL.Query().Get("status")
	unitID := r.URL.Query().Get("unit_id")

	var requests []storage.MaintenanceRequest
	var err error

	if status != "" {
		requests, err = h.maintenanceService.GetRequestsByStatus(r.Context(), userID, status)
	} else if unitID != "" {
		requests, err = h.maintenanceService.GetRequestsByUnit(r.Context(), unitID, userID)
	} else {
		requests, err = h.maintenanceService.GetRequestsByOwner(r.Context(), userID)
	}

	if err != nil {
		h.logger.Error("Failed to get maintenance requests", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get maintenance requests: "+err.Error())
		return
	}

	response := dto.MaintenanceRequestsToResponse(requests)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetRequest(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	requestID := chi.URLParam(r, "id")

	maintenanceReq, err := h.maintenanceService.GetRequestByID(r.Context(), requestID, userID)
	if err != nil {
		h.logger.Error("Failed to get maintenance request", zap.Error(err), zap.String("request_id", requestID))
		utils.WriteErrorResponse(w, http.StatusNotFound, "Maintenance request not found")
		return
	}

	response := dto.MaintenanceRequestToResponse(maintenanceReq)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) UpdateRequest(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	requestID := chi.URLParam(r, "id")

	var req dto.UpdateMaintenanceRequestRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode maintenance request update", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if validationErrors := req.Validate(); len(validationErrors) > 0 {
		h.logger.Warn("Maintenance request update validation failed", zap.Any("errors", validationErrors))
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return
	}

	maintenanceReq, err := h.maintenanceService.UpdateRequest(r.Context(), requestID, userID, req.Status, req.AssignedTo, req.Cost, req.Description)
	if err != nil {
		h.logger.Error("Failed to update maintenance request", zap.Error(err), zap.String("request_id", requestID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to update maintenance request: "+err.Error())
		return
	}

	response := dto.MaintenanceRequestToResponse(maintenanceReq)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) DeleteRequest(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	requestID := chi.URLParam(r, "id")

	err := h.maintenanceService.DeleteRequest(r.Context(), requestID, userID)
	if err != nil {
		h.logger.Error("Failed to delete maintenance request", zap.Error(err), zap.String("request_id", requestID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to delete maintenance request: "+err.Error())
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (h *MaintenanceHandler) GetPendingRequests(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	requests, err := h.maintenanceService.GetPendingRequests(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get pending maintenance requests", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get pending requests")
		return
	}

	response := dto.MaintenanceRequestsToResponse(requests)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetActiveRequests(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	requests, err := h.maintenanceService.GetActiveRequests(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get active maintenance requests", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get active requests")
		return
	}

	response := dto.MaintenanceRequestsToResponse(requests)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetSummary(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())

	summary, err := h.maintenanceService.GetMaintenanceSummary(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get maintenance summary", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get maintenance summary")
		return
	}

	response := dto.MaintenanceSummaryToResponse(summary)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetRequestsByUnit(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	unitID := chi.URLParam(r, "unit_id")

	requests, err := h.maintenanceService.GetRequestsByUnit(r.Context(), unitID, userID)
	if err != nil {
		h.logger.Error("Failed to get maintenance requests by unit", zap.Error(err), zap.String("unit_id", unitID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get maintenance requests: "+err.Error())
		return
	}

	response := dto.MaintenanceRequestsToResponse(requests)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) CreateAttachment(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	requestID := chi.URLParam(r, "id")

	var req dto.CreateAttachmentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode attachment request", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if validationErrors := req.Validate(); len(validationErrors) > 0 {
		h.logger.Warn("Attachment validation failed", zap.Any("errors", validationErrors))
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": validationErrors,
		})
		return
	}

	attachment, err := h.maintenanceService.CreateAttachment(r.Context(), requestID, userID, req.URL, req.Filename, req.FileSize, req.ContentType, userID)
	if err != nil {
		h.logger.Error("Failed to create attachment", zap.Error(err), zap.String("request_id", requestID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create attachment: "+err.Error())
		return
	}

	response := dto.AttachmentToResponse(attachment)
	utils.WriteSuccessResponse(w, response)
}

func (h *MaintenanceHandler) GetAttachments(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	requestID := chi.URLParam(r, "id")

	attachments, err := h.maintenanceService.GetAttachments(r.Context(), requestID, userID)
	if err != nil {
		h.logger.Error("Failed to get attachments", zap.Error(err), zap.String("request_id", requestID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get attachments: "+err.Error())
		return
	}

	response := dto.AttachmentsToResponse(attachments)
	utils.WriteSuccessResponse(w, response)
}