package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"

	"go.uber.org/zap"
)

type ProfileHandler struct {
	authService *service.AuthService
	jwtService  *auth.JWTService
	config      *config.Config
	logger      *zap.Logger
}

func NewProfileHandler(authService *service.AuthService, jwtService *auth.JWTService, config *config.Config, logger *zap.Logger) *ProfileHandler {
	return &ProfileHandler{
		authService: authService,
		jwtService:  jwtService,
		config:      config,
		logger:      logger,
	}
}

func (h *<PERSON>Handler) GetProfile(w http.ResponseWriter, r *http.Request) {
	userID := r.Context().Value("user_id").(string)

	user, err := h.authService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get user profile", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get profile")
		return
	}

	userName := ""
	if user.Name != nil {
		userName = *user.Name
	}

	response := dto.ProfileResponse{
		ID:        user.ID,
		Email:     user.Email,
		Name:      userName,
		CreatedAt: user.CreatedAt,
	}

	utils.WriteSuccessResponse(w, response, "Profile retrieved successfully")
}

func (h *ProfileHandler) UpdateProfile(w http.ResponseWriter, r *http.Request) {
	userID := r.Context().Value("user_id").(string)

	var req dto.UpdateProfileRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]any{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Update user profile
	user, err := h.authService.UpdateProfile(r.Context(), userID, req.Name)
	if err != nil {
		h.logger.Error("Failed to update user profile", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to update profile")
		return
	}

	// Generate new access token with updated name
	userName := ""
	if user.Name != nil {
		userName = *user.Name
	}

	accessToken, err := h.jwtService.GenerateToken(user.ID, user.Email, userName)
	if err != nil {
		h.logger.Error("Failed to generate new access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to generate new token")
		return
	}

	response := dto.UpdateProfileResponse{
		User: dto.ProfileResponse{
			ID:        user.ID,
			Email:     user.Email,
			Name:      userName,
			CreatedAt: user.CreatedAt,
		},
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
	}

	utils.WriteSuccessResponse(w, response, "Profile updated successfully")
}

func (h *ProfileHandler) ChangePassword(w http.ResponseWriter, r *http.Request) {
	userID := r.Context().Value("user_id").(string)

	var req dto.ChangePasswordRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]any{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Change password
	err := h.authService.ChangePassword(r.Context(), userID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		if err == service.ErrInvalidCredentials {
			utils.WriteErrorResponse(w, http.StatusBadRequest, "Current password is incorrect")
			return
		}
		h.logger.Error("Failed to change password", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to change password")
		return
	}

	utils.WriteSuccessResponse(w, nil, "Password changed successfully")
}