package routes

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/nyunja/rentbase/backend/internal/api/handlers"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/notification"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

func SetupRoutes(r chi.Router, db *pgxpool.Pool, cfg *config.Config, logger *zap.Logger) {
	// Initialize stores
	userStore := storage.NewUserStore(db)
	propertyStore := storage.NewPropertyStore(db)
	leaseStore := storage.NewLeaseStore(db)
	paymentStore := storage.NewPaymentStore(db)
	maintenanceStore := storage.NewMaintenanceStore(db)
	dashboardStore := storage.NewDashboardStore(db)
	notificationStore := storage.NewNotificationStore(db)

	// Initialize services
	jwtService := auth.NewJWTService(cfg.JWT.Secret, cfg.JWT.ExpiresIn)
	refreshTokenService := auth.NewRefreshTokenService(db, cfg.JWT.RefreshTokenExpiresIn)

	// Initialize notification service
	notificationService := notification.NewService(
		notificationStore,
		leaseStore,
		paymentStore,
		maintenanceStore,
		cfg.Twilio,
		logger,
	)

	authService := service.NewAuthService(userStore)
	propertyService := service.NewPropertyService(propertyStore)
	leaseService := service.NewLeaseService(leaseStore, notificationService)
	paymentService := service.NewPaymentService(paymentStore, leaseStore)
	maintenanceService := service.NewMaintenanceService(maintenanceStore, propertyStore, leaseStore)
	dashboardService := service.NewDashboardService(dashboardStore, paymentStore)

	// Initialize middleware for auth
	authMiddleware := middleware.NewAuthMiddleware(jwtService)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, jwtService, refreshTokenService, cfg, logger)
	profileHandler := handlers.NewProfileHandler(authService, jwtService, cfg, logger)
	propertyHandler := handlers.NewPropertyHandler(propertyService, logger)
	leaseHandler := handlers.NewLeaseHandler(leaseService, logger)
	paymentHandler := handlers.NewPaymentHandler(paymentService, logger)
	maintenanceHandler := handlers.NewMaintenanceHandler(maintenanceService, logger)
	dashboardHandler := handlers.NewDashboardHandler(dashboardService, logger)

	// API v1 routes
	r.Route("/api/v1", func(r chi.Router) {
		// Health check (already defined in main.go, but can be here too)
		r.Get("/ping", func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"pong","service":"rental-property-mvp"}`))
		})

		// Public routes
		r.Route("/auth", func(r chi.Router) {
			r.Post("/signup", authHandler.Signup)
			r.Post("/register", authHandler.Signup)
			r.Post("/login", authHandler.Login)
			r.Post("/refresh", authHandler.RefreshToken)
			r.Post("/logout", authHandler.Logout)
		})

		// Profile routes (protected)
		r.Route("/profile", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", profileHandler.GetProfile)
			r.Put("/", profileHandler.UpdateProfile)
			r.Post("/change-password", profileHandler.ChangePassword)
		})

		// Properties routes (protected)
		r.Route("/properties", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", propertyHandler.GetProperties)
			r.Post("/", propertyHandler.CreateProperty)
			r.Get("/{id}", propertyHandler.GetProperty)
			r.Post("/{id}/units", propertyHandler.CreateUnit)
			r.Get("/{id}/units", func(w http.ResponseWriter, r *http.Request) {
				propertyID := chi.URLParam(r, "id")
				userID := middleware.GetUserIDFromContext(r.Context())

				// Verify ownership
				_, err := propertyService.GetPropertyByID(r.Context(), propertyID, userID)
				if err != nil {
					utils.WriteErrorResponse(w, http.StatusNotFound, "Property not found")
					return
				}

				units, err := propertyService.GetUnitsByProperty(r.Context(), propertyID)
				if err != nil {
					logger.Error("Failed to get units", zap.Error(err))
					utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get units")
					return
				}

				utils.WriteSuccessResponse(w, units)
			})
		})

		// Leases routes (protected)
		r.Route("/leases", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", leaseHandler.GetLeases)
			r.Post("/", leaseHandler.CreateLease)
			r.Get("/{id}", leaseHandler.GetLease)
			r.Put("/{id}/deactivate", leaseHandler.DeactivateLease)
		})

		// Tenants routes (protected)
		r.Route("/tenants", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", leaseHandler.GetTenants)
			r.Post("/", leaseHandler.CreateTenant)
			r.Get("/{id}", leaseHandler.GetTenant)
		})

		// Payments routes (protected)
		r.Route("/payments", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", paymentHandler.GetPayments)
			r.Post("/", paymentHandler.CreatePayment)
			r.Get("/recent", paymentHandler.GetRecentPayments)
			r.Get("/summary", paymentHandler.GetPaymentSummary)
			r.Get("/monthly/{year}/{month}", paymentHandler.GetMonthlyPayments)
			r.Get("/{id}", paymentHandler.GetPayment)
			r.Put("/{id}", paymentHandler.UpdatePayment)
			r.Delete("/{id}", paymentHandler.DeletePayment)
		})

		// Maintenance routes (protected)
		r.Route("/maintenance", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Get("/", maintenanceHandler.GetRequests)
			r.Post("/", maintenanceHandler.CreateRequest)
			r.Get("/pending", maintenanceHandler.GetPendingRequests)
			r.Get("/active", maintenanceHandler.GetActiveRequests)
			r.Get("/summary", maintenanceHandler.GetSummary)
			r.Get("/unit/{unit_id}", maintenanceHandler.GetRequestsByUnit)
			r.Get("/{id}", maintenanceHandler.GetRequest)
			r.Put("/{id}", maintenanceHandler.UpdateRequest)
			r.Delete("/{id}", maintenanceHandler.DeleteRequest)
			r.Post("/{id}/attachments", maintenanceHandler.CreateAttachment)
			r.Get("/{id}/attachments", maintenanceHandler.GetAttachments)
		})

		// Dashboard routes (protected)
		r.Route("/dashboard", func(r chi.Router) {
			r.Use(authMiddleware.RequireAuth)
			r.Route("/", func(r chi.Router) {
				r.Get("/summary", dashboardHandler.GetSummary)

				r.Get("/stats", func(w http.ResponseWriter, r *http.Request) {
					userID := middleware.GetUserIDFromContext(r.Context())

					stats, err := dashboardStore.GetStats(r.Context(), userID)
					if err != nil {
						logger.Error("Failed to get dashboard stats", zap.Error(err))
						utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get stats")
						return
					}

					utils.WriteSuccessResponse(w, stats)
				})
			})
		})
	})
}
