package dto

import (
	"time"

	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
)

type CreatePaymentRequest struct {
	LeaseID       string  `json:"lease_id"`
	Amount        float64 `json:"amount"`
	PaidAt        string  `json:"paid_at"`
	PaymentMethod *string `json:"payment_method,omitempty"`
	Notes         *string `json:"notes,omitempty"`
}

func (r *CreatePaymentRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateRequired(r.<PERSON><PERSON>, "lease_id"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.Amount <= 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "amount",
			Message: "Amount must be positive",
		})
	}

	if r.PaidAt == "" {
		errors = append(errors, utils.ValidationError{
			Field:   "paid_at",
			Message: "Payment date is required",
		})
	} else {
		_, err := time.Parse("2006-01-02T15:04:05Z07:00", r.<PERSON>id<PERSON>)
		if err != nil {
			_, err = time.Parse("2006-01-02", r.PaidAt)
			if err != nil {
				errors = append(errors, utils.ValidationError{
					Field:   "paid_at",
					Message: "Invalid date format. Use ISO 8601 format or YYYY-MM-DD",
				})
			}
		}
	}

	if r.PaymentMethod != nil {
		validMethods := map[string]bool{
			"manual":        true,
			"mpesa":         true,
			"card":          true,
			"bank_transfer": true,
			"cash":          true,
			"check":         true,
		}
		if !validMethods[*r.PaymentMethod] {
			errors = append(errors, utils.ValidationError{
				Field:   "payment_method",
				Message: "Invalid payment method",
			})
		}
	}

	return errors
}

func (r *CreatePaymentRequest) GetPaidAtTime() (time.Time, error) {
	if r.PaidAt == "" {
		return time.Now(), nil
	}

	t, err := time.Parse("2006-01-02T15:04:05Z07:00", r.PaidAt)
	if err != nil {
		t, err = time.Parse("2006-01-02", r.PaidAt)
		if err != nil {
			return time.Time{}, err
		}
	}
	return t, nil
}

type UpdatePaymentRequest struct {
	Amount        float64 `json:"amount"`
	PaidAt        string  `json:"paid_at"`
	PaymentMethod *string `json:"payment_method,omitempty"`
	Notes         *string `json:"notes,omitempty"`
}

func (r *UpdatePaymentRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if r.Amount <= 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "amount",
			Message: "Amount must be positive",
		})
	}

	if r.PaidAt == "" {
		errors = append(errors, utils.ValidationError{
			Field:   "paid_at",
			Message: "Payment date is required",
		})
	} else {
		_, err := time.Parse("2006-01-02T15:04:05Z07:00", r.PaidAt)
		if err != nil {
			_, err = time.Parse("2006-01-02", r.PaidAt)
			if err != nil {
				errors = append(errors, utils.ValidationError{
					Field:   "paid_at",
					Message: "Invalid date format. Use ISO 8601 format or YYYY-MM-DD",
				})
			}
		}
	}

	if r.PaymentMethod != nil {
		validMethods := map[string]bool{
			"manual":        true,
			"mpesa":         true,
			"card":          true,
			"bank_transfer": true,
			"cash":          true,
			"check":         true,
		}
		if !validMethods[*r.PaymentMethod] {
			errors = append(errors, utils.ValidationError{
				Field:   "payment_method",
				Message: "Invalid payment method",
			})
		}
	}

	return errors
}

func (r *UpdatePaymentRequest) GetPaidAtTime() (time.Time, error) {
	t, err := time.Parse("2006-01-02T15:04:05Z07:00", r.PaidAt)
	if err != nil {
		t, err = time.Parse("2006-01-02", r.PaidAt)
		if err != nil {
			return time.Time{}, err
		}
	}
	return t, nil
}

type PaymentResponse struct {
	ID            string  `json:"id"`
	LeaseID       string  `json:"lease_id"`
	Amount        float64 `json:"amount"`
	PaidAt        string  `json:"paid_at"`
	PaymentMethod *string `json:"payment_method"`
	Notes         *string `json:"notes"`
	CreatedBy     *string `json:"created_by"`
	CreatedAt     string  `json:"created_at"`

	LeaseRentAmount *float64 `json:"lease_rent_amount,omitempty"`
	UnitName        *string  `json:"unit_name,omitempty"`
	TenantName      *string  `json:"tenant_name,omitempty"`
}

func PaymentToResponse(payment *storage.Payment) PaymentResponse {
	return PaymentResponse{
		ID:              payment.ID,
		LeaseID:         payment.LeaseID,
		Amount:          payment.Amount,
		PaidAt:          payment.PaidAt.Format("2006-01-02T15:04:05Z07:00"),
		PaymentMethod:   payment.PaymentMethod,
		Notes:           payment.Notes,
		CreatedBy:       payment.CreatedBy,
		CreatedAt:       payment.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		LeaseRentAmount: payment.LeaseRentAmount,
		UnitName:        payment.UnitName,
		TenantName:      payment.TenantName,
	}
}

func PaymentsToResponse(payments []storage.Payment) []PaymentResponse {
	var responses []PaymentResponse
	for _, payment := range payments {
		responses = append(responses, PaymentToResponse(&payment))
	}
	return responses
}

type PaymentSummaryResponse struct {
	TotalAmount  float64 `json:"total_amount"`
	PaymentCount int     `json:"payment_count"`
	PeriodStart  string  `json:"period_start"`
	PeriodEnd    string  `json:"period_end"`
}

func PaymentSummaryToResponse(summary *storage.PaymentSummary) PaymentSummaryResponse {
	return PaymentSummaryResponse{
		TotalAmount:  summary.TotalAmount,
		PaymentCount: summary.PaymentCount,
		PeriodStart:  summary.PeriodStart.Format("2006-01-02T15:04:05Z07:00"),
		PeriodEnd:    summary.PeriodEnd.Format("2006-01-02T15:04:05Z07:00"),
	}
}