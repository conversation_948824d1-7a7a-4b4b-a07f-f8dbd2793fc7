package dto

import (
	"time"

	"github.com/nyunja/rentbase/backend/internal/utils"
)

type ProfileResponse struct {
	ID        string    `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
}

type UpdateProfileRequest struct {
	Name string `json:"name"`
}

func (u *UpdateProfileRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateAlphanumeric(u.Name, "name"); err.Message != "" {
		errors = append(errors, err)
	}

	return errors
}

type UpdateProfileResponse struct {
	User        ProfileResponse `json:"user"`
	AccessToken string          `json:"access_token"`
	TokenType   string          `json:"token_type"`
	ExpiresIn   int             `json:"expires_in"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password"`
	NewPassword     string `json:"new_password"`
}

func (c *ChangePasswordRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateRequired(c.CurrentPassword, "current_password"); err.Message != "" {
		errors = append(errors, err)
	}

	if err := utils.ValidatePassword(c.NewPassword); err.Message != "" {
		errors = append(errors, err)
	}

	return errors
}