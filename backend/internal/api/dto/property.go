package dto

import (
	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
)

type CreatePropertyRequest struct {
	Title       string `json:"title"`
	Address     string `json:"address"`
	City        string `json:"city,omitempty"`
	Country     string `json:"country,omitempty"`
	Description string `json:"description,omitempty"`
}

func (r *CreatePropertyRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateAlphanumeric(r.Title, "title"); err.Message != "" {
		errors = append(errors, err)
	}

	if err := utils.ValidateAlphanumeric(r.Address, "address"); err.Message != "" {
		errors = append(errors, err)
	}

	return errors
}

type PropertyResponse struct {
	ID          string  `json:"id"`
	OwnerID     string  `json:"owner_id"`
	Title       string  `json:"title"`
	Address     string  `json:"address"`
	City        *string `json:"city"`
	Country     *string `json:"country"`
	Description *string `json:"description"`
	UnitsCount  int     `json:"units_count"`
	CreatedAt   string  `json:"created_at"`
	UpdatedAt   string  `json:"updated_at"`
}

func PropertyToResponse(property *storage.Property) PropertyResponse {
	return PropertyResponse{
		ID:          property.ID,
		OwnerID:     property.OwnerID,
		Title:       property.Title,
		Address:     property.Address,
		City:        property.City,
		Country:     property.Country,
		Description: property.Description,
		UnitsCount:  property.UnitsCount,
		CreatedAt:   property.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   property.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}

func PropertiesToResponse(properties []storage.Property) []PropertyResponse {
	var responses []PropertyResponse
	for _, property := range properties {
		responses = append(responses, PropertyToResponse(&property))
	}
	return responses
}

type CreateUnitRequest struct {
	Name       string  `json:"name"`
	RentAmount float64 `json:"rent_amount"`
	Status     string  `json:"status,omitempty"`
}

func (r *CreateUnitRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateAlphanumeric(r.Name, "name"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.RentAmount < 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "rent_amount",
			Message: "Rent amount must be non-negative",
		})
	}

	return errors
}

type UnitResponse struct {
	ID         string  `json:"id"`
	PropertyID string  `json:"property_id"`
	Name       string  `json:"name"`
	RentAmount float64 `json:"rent_amount"`
	Status     string  `json:"status"`
	CreatedAt  string  `json:"created_at"`
	UpdatedAt  string  `json:"updated_at"`
}

func UnitToResponse(unit *storage.Unit) UnitResponse {
	return UnitResponse{
		ID:         unit.ID,
		PropertyID: unit.PropertyID,
		Name:       unit.Name,
		RentAmount: unit.RentAmount,
		Status:     unit.Status,
		CreatedAt:  unit.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:  unit.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}

func UnitsToResponse(units []storage.Unit) []UnitResponse {
	var responses []UnitResponse
	for _, unit := range units {
		responses = append(responses, UnitToResponse(&unit))
	}
	return responses
}