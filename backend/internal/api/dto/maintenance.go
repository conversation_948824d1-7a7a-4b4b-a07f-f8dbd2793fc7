package dto

import (
	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
)

type CreateMaintenanceRequestRequest struct {
	UnitID      *string `json:"unit_id,omitempty"`
	LeaseID     *string `json:"lease_id,omitempty"`
	Title       string  `json:"title"`
	Description *string `json:"description,omitempty"`
	Priority    string  `json:"priority"`
}

func (r *CreateMaintenanceRequestRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateAlphanumeric(r.Title, "title"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.UnitID == nil && r.LeaseID == nil {
		errors = append(errors, utils.ValidationError{
			Field:   "unit_id or lease_id",
			Message: "Either unit_id or lease_id must be provided",
		})
	}

	validPriorities := map[string]bool{
		"low":    true,
		"medium": true,
		"high":   true,
		"urgent": true,
	}
	if !validPriorities[r.Priority] {
		errors = append(errors, utils.ValidationError{
			Field:   "priority",
			Message: "Priority must be one of: low, medium, high, urgent",
		})
	}

	return errors
}

type UpdateMaintenanceRequestRequest struct {
	Status      *string  `json:"status,omitempty"`
	AssignedTo  *string  `json:"assigned_to,omitempty"`
	Cost        *float64 `json:"cost,omitempty"`
	Description *string  `json:"description,omitempty"`
}

func (r *UpdateMaintenanceRequestRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if r.Status != nil {
		validStatuses := map[string]bool{
			"pending":     true,
			"in_progress": true,
			"resolved":    true,
			"cancelled":   true,
		}
		if !validStatuses[*r.Status] {
			errors = append(errors, utils.ValidationError{
				Field:   "status",
				Message: "Status must be one of: pending, in_progress, resolved, cancelled",
			})
		}
	}

	if r.Cost != nil && *r.Cost < 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "cost",
			Message: "Cost cannot be negative",
		})
	}

	return errors
}

type MaintenanceRequestResponse struct {
	ID          string  `json:"id"`
	UnitID      *string `json:"unit_id"`
	LeaseID     *string `json:"lease_id"`
	Title       string  `json:"title"`
	Description *string `json:"description"`
	Status      string  `json:"status"`
	Priority    string  `json:"priority"`
	CreatedBy   *string `json:"created_by"`
	AssignedTo  *string `json:"assigned_to"`
	Cost        float64 `json:"cost"`
	CreatedAt   string  `json:"created_at"`
	ResolvedAt  *string `json:"resolved_at"`

	// Joined fields
	UnitName      *string `json:"unit_name,omitempty"`
	PropertyID    *string `json:"property_id,omitempty"`
	PropertyTitle *string `json:"property_title,omitempty"`
	TenantName    *string `json:"tenant_name,omitempty"`
	TenantEmail   *string `json:"tenant_email,omitempty"`
	LeaseActive   *bool   `json:"lease_active,omitempty"`
}

func MaintenanceRequestToResponse(req *storage.MaintenanceRequest) MaintenanceRequestResponse {
	response := MaintenanceRequestResponse{
		ID:            req.ID,
		UnitID:        req.UnitID,
		LeaseID:       req.LeaseID,
		Title:         req.Title,
		Description:   req.Description,
		Status:        req.Status,
		Priority:      req.Priority,
		CreatedBy:     req.CreatedBy,
		AssignedTo:    req.AssignedTo,
		Cost:          req.Cost,
		CreatedAt:     req.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UnitName:      req.UnitName,
		PropertyID:    req.PropertyID,
		PropertyTitle: req.PropertyTitle,
		TenantName:    req.TenantName,
		TenantEmail:   req.TenantEmail,
		LeaseActive:   req.LeaseActive,
	}

	if req.ResolvedAt != nil {
		resolvedAtStr := req.ResolvedAt.Format("2006-01-02T15:04:05Z07:00")
		response.ResolvedAt = &resolvedAtStr
	}

	return response
}

func MaintenanceRequestsToResponse(requests []storage.MaintenanceRequest) []MaintenanceRequestResponse {
	var responses []MaintenanceRequestResponse
	for _, req := range requests {
		responses = append(responses, MaintenanceRequestToResponse(&req))
	}
	return responses
}

type MaintenanceSummaryResponse struct {
	PendingCount      int      `json:"pending_count"`
	InProgressCount   int      `json:"in_progress_count"`
	ResolvedCount     int      `json:"resolved_count"`
	UrgentCount       int      `json:"urgent_count"`
	HighCount         int      `json:"high_count"`
	TotalCost         float64  `json:"total_cost"`
	AvgResolutionDays *float64 `json:"avg_resolution_days"`
}

func MaintenanceSummaryToResponse(summary *storage.MaintenanceSummary) MaintenanceSummaryResponse {
	return MaintenanceSummaryResponse{
		PendingCount:      summary.PendingCount,
		InProgressCount:   summary.InProgressCount,
		ResolvedCount:     summary.ResolvedCount,
		UrgentCount:       summary.UrgentCount,
		HighCount:         summary.HighCount,
		TotalCost:         summary.TotalCost,
		AvgResolutionDays: summary.AvgResolutionDays,
	}
}

type CreateAttachmentRequest struct {
	URL         string  `json:"url"`
	Filename    *string `json:"filename,omitempty"`
	FileSize    *int64  `json:"file_size,omitempty"`
	ContentType *string `json:"content_type,omitempty"`
}

func (r *CreateAttachmentRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateURL(r.URL, "url"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.FileSize != nil && *r.FileSize < 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "file_size",
			Message: "File size cannot be negative",
		})
	}

	return errors
}

type AttachmentResponse struct {
	ID          string  `json:"id"`
	ParentType  string  `json:"parent_type"`
	ParentID    string  `json:"parent_id"`
	URL         string  `json:"url"`
	Filename    *string `json:"filename"`
	FileSize    *int64  `json:"file_size"`
	ContentType *string `json:"content_type"`
	UploadedBy  *string `json:"uploaded_by"`
	UploadedAt  string  `json:"uploaded_at"`
}

func AttachmentToResponse(attachment *storage.Attachment) AttachmentResponse {
	return AttachmentResponse{
		ID:          attachment.ID,
		ParentType:  attachment.ParentType,
		ParentID:    attachment.ParentID,
		URL:         attachment.URL,
		Filename:    attachment.Filename,
		FileSize:    attachment.FileSize,
		ContentType: attachment.ContentType,
		UploadedBy:  attachment.UploadedBy,
		UploadedAt:  attachment.UploadedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}

func AttachmentsToResponse(attachments []storage.Attachment) []AttachmentResponse {
	var responses []AttachmentResponse
	for _, attachment := range attachments {
		responses = append(responses, AttachmentToResponse(&attachment))
	}
	return responses
}