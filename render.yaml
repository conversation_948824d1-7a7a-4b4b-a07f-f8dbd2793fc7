services:
  # PostgreSQL Database
  - type: pserv
    name: rentbase-db
    env: postgresql
    plan: free
    databaseName: rentbase
    user: rentbase_user

  # Go Backend Service
  - type: web
    name: rentbase-backend
    runtime: go
    repo: https://github.com/yourusername/rentbase.git
    rootDir: ./backend
    buildCommand: go build -o main cmd/server/main.go
    startCommand: ./main
    plan: free
    envVars:
      - key: PORT
        value: 10000
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: rentbase-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRES_IN
        value: 15m
      - key: REFRESH_TOKEN_EXPIRES_IN
        value: 7d
    healthCheckPath: /api/v1/health